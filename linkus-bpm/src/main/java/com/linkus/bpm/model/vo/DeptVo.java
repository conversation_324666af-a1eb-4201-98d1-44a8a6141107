package com.linkus.bpm.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.bson.types.ObjectId;
@Data
public class DeptVo {
    @ExcelIgnore
    private ObjectId id;
    @ExcelProperty(value = "序号")
    private String no;
    @ExcelProperty(value = "事业部/中心")
    private String division;
    @ExcelProperty(value = "虚拟中心")
    private String virtualCenter;
    @ExcelProperty(value = "管理部门")
    private String dept;
    @ExcelProperty(value = "负责人")
    private String resp;
    @ExcelProperty(value = "调整说明")
    @ExcelIgnore
    private String desc;
    @ExcelIgnore
    private Integer defNo;
    private String codeName;
}
