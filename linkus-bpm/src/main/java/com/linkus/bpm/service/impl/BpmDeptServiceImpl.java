package com.linkus.bpm.service.impl;

import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_B;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_R;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.pager.Pager;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.response.BusinessException;
import com.linkus.base.util.BeanMapUtils;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.PageBean;
import com.linkus.base.util.StringUtil;
import com.linkus.bpm.constants.BpmConstants;
import com.linkus.bpm.dao.IBpmProcessDao;
import com.linkus.bpm.model.PermList;
import com.linkus.bpm.model.vo.DeptRecordVo;
import com.linkus.bpm.model.vo.DeptVo;
import com.linkus.bpm.model.vo.TreeNode;
import com.linkus.bpm.service.IBpmApplyService;
import com.linkus.bpm.service.IBpmDeptService;
import com.linkus.sys.dao.SysDefDao;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeDefType;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sys.service.ISysDefService;
import com.linkus.sysuser.dao.ISysDefClickLogDao;
import com.linkus.sysuser.model.TeSysDefClickLog;
import com.linkus.sysuser.model.TeSysDefClickLog2DefType;
import com.linkus.sysuser.model.TeSysDefClickLog2User;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import com.linkus.sysuser.model.TeSysDefRoleUser2User;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysDefClickLogService;
import com.linkus.sysuser.service.ISysDefRoleUserService;
import com.linkus.sysuser.service.ISysUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class BpmDeptServiceImpl implements IBpmDeptService {
    @Autowired
    private ISysDefService sysDefService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private SysDefDao sysDefDao;

    @Autowired
    private ISysDefRoleUserService sysDefRoleUserService;

    @Autowired
    private ISysDefClickLogService sysDefClickLogService;

    @Autowired
    private ISysDefClickLogDao sysDefClickLogDao;

    @Autowired
    private IBpmProcessDao bpmProcessDao;

    @Autowired
    private IBpmApplyService bpmApplyService;

    @Override
    public TeSysDef createDept(ObjectId parentDefId,ObjectId deptRespUserId, String deptName
            , String desc,Integer defNo,String codeName, TeSysUser loginUser) {
        // 1、取出所有bpm部门
        List<SysDef> bpmDepts = sysDefService.getSysDefsByDefType(SysDefTypeCodeName.BPM_DEPT);

        TeSysDef sysDef = sysDefService.getTeSysDefById(parentDefId);
        if (null == sysDef) {
            throw BusinessException.initExc("上级部门不存在");
        }
        List<String> defNames = bpmDepts.stream().map(SysDef::getDefName).collect(Collectors.toList());
        if (defNames.contains(deptName)) {
            throw BusinessException.initExc("该部门已存在，请重新填写");
        }
        List<SysDef> defList = sysDefService.getSysDefByParentDefId(parentDefId);
        Optional<SysDef> first = defList.stream().max(Comparator.comparing(SysDef::getDefNo));
        TeSysDef teSysDef = new TeSysDef();
        BeanUtils.copyProperties(sysDef, teSysDef);
        teSysDef.setId(null);
        teSysDef.setDefName(deptName);
        teSysDef.setDefNo(sysDef.getDefNo()+100);
        teSysDef.setAddUser(loginUser.trans2User());
        teSysDef.setAddTime(new Date());
        teSysDef.setParentDefId(parentDefId);
        teSysDef.setDefDesc(desc);
        if (null != defNo) {
            teSysDef.setDefNo(defNo+10);
        } else {
            if (first.isPresent()){
                teSysDef.setDefNo(first.get().getDefNo()+100);
            } else {
                teSysDef.setDefNo(sysDef.getDefNo()+100);
            }
        }
        if (StringUtil.isNotNull(codeName)) {
            teSysDef.setCodeName(codeName);
        }
        String parentDefPath = sysDef.getParentDefPath() == null ? "," : sysDef.getParentDefPath();
        teSysDef.setParentDefPath(parentDefPath +parentDefId.toHexString()+",");
        TeSysDef insert = sysDefDao.insert(teSysDef);

        if (null != deptRespUserId && null != insert.getId()) {
            sysDefRoleUserService.addSysUserRoles(deptRespUserId, BpmConstants.DEF_ID_ROLE_RESP_ID, Collections.singletonList(insert.getId()), loginUser);
        }

        String clickValue = "新增部门："+deptName+"，上级部门："+sysDef.getDefName();
        addClickLog(BpmConstants.DEF_ID_BPM_DEPT_INSERT_PAGE_WIDGET,desc, loginUser, sysDef, clickValue);
        return insert;
    }

    @Override
    public TeSysDef updateDept(ObjectId defId,ObjectId deptRespUserId, ObjectId updateParentDefId
            , String updateDeptName, ObjectId updateUserId, String desc,Integer defNo, String codeName,TeSysUser loginUser) {
        TeSysDef sysDef = sysDefService.getTeSysDefById(defId);
        TeSysDef srcDef = new TeSysDef();
        BeanUtils.copyProperties(sysDef,srcDef);
        String clickValue = "";
        if (null == sysDef) {
            throw BusinessException.initExc("部门不存在");
        }
        if (null == updateParentDefId && StringUtil.isNull(updateDeptName) && StringUtil.isNull(updateUserId)) {
            throw BusinessException.initExc("变更列不能都为空");
        }
        if (null != updateParentDefId){
            ObjectId parentDefId = sysDef.getParentDefId();
            if (!updateParentDefId.equals(parentDefId)) {
                TeSysDef parentDef = sysDefService.getTeSysDefById(parentDefId);
                TeSysDef updateParentDef = sysDefService.getTeSysDefById(updateParentDefId);
                sysDef.setParentDefId(updateParentDefId);
                clickValue = "将原上级部门："+parentDef.getDefName()+"，修改为："+updateParentDef.getDefName()+"；";
            }
        }
        if (StringUtil.isNotNull(updateDeptName)) {
            String defName = sysDef.getDefName();
            if (!defName.equals(updateDeptName)) {
                sysDef.setDefName(updateDeptName);
                clickValue += "将原部门名称："+defName+"，修改为："+updateDeptName+"；";

            }
        }
        if (StringUtil.isNotNull(updateUserId) && StringUtil.isNotNull(deptRespUserId) && !updateUserId.equals(deptRespUserId) ) {
            TeSysDefRoleUser sysDefRoleUser = sysDefRoleUserService.querySysDefRoleUser(defId, BpmConstants.DEF_ID_ROLE_RESP_ID, deptRespUserId);
            String userName = sysDefRoleUser.getRoleUser().getUserName();
            if (null == sysDefRoleUser) {
                throw BusinessException.initExc("原部门负责人不存在");
            }
            TeSysUser sysUser = sysUserService.findById(updateUserId);
            if (null == sysUser) {
                throw BusinessException.initExc("修改部门负责人不存在");
            }
            TeSysDefRoleUser2User roleUser = sysDefRoleUser.getRoleUser();
            roleUser.setLoginName(sysUser.getLoginName());
            roleUser.setUserId(sysUser.getId());
            roleUser.setUserName(sysUser.getUserName());
            roleUser.setJobCode(sysUser.getJobCode());
            sysDefRoleUser.setRoleUser(roleUser);

            sysDefRoleUserService.updateById(sysDefRoleUser.getId(),sysDefRoleUser);
            clickValue += "将原部门负责人："+ userName +"，修改为："+sysUser.getUserName();
        }
        if (null != defNo){
            sysDef.setDefNo(defNo);
        }
        if (StringUtil.isNotNull(codeName)) {
            sysDef.setCodeName(codeName);
        }
        sysDef.setDefDesc(desc);
        sysDefService.updateSysDef(sysDef);

        // 去掉末尾的中文分号
        if (clickValue.endsWith("；")) {
            clickValue = clickValue.substring(0, clickValue.length() - 1);
        }
        addClickLog(BpmConstants.DEF_ID_BPM_DEPT_UPDATE_PAGE_WIDGET,desc, loginUser, srcDef, clickValue);
        return sysDef;
    }

    @Override
    public void deleteDept(ObjectId defId, String desc, TeSysUser loginUser) {
        TeSysDef sysDef = sysDefService.getTeSysDefById(defId);
        if (null == sysDef) {
            throw BusinessException.initExc("部门不存在");
        }
        List<SysDef> defList = sysDefService.getSysDefByParentDefId(defId);
        if (CollectionUtils.isNotEmpty(defList)) {
            throw BusinessException.initExc("请先转移下级部门再删除当前部门");
        }

        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.bpmProcess_isValid, true));
        conds.add(new DC_E(DFN.bpmProcess_dept.dot(DFN.common_cid), defId));
        long count = bpmProcessDao.countByConds(conds);
        if (count > 0) {
            throw BusinessException.initExc("该目录下存在流程制度，不允许删除");
        }

        SysDef other = sysDefService.getSysDefById(BpmConstants.DEF_ID_BPM_DEPT_OTHER);

        conds.clear();
        conds.add(new DC_E(DFN.bpmProcess_isValid, false));
        conds.add(new DC_E(DFN.bpmProcess_dept.dot(DFN.common_cid), defId));

        List<UpdataData> updates = new ArrayList<>();
        updates.add(new UpdataData(DFN.bpmProcess_dept, other.trans2IdName()));
        bpmProcessDao.updateByConds(conds,updates);

        String clickValue = "删除部门："+sysDef.getDefName();
        sysDefService.removeSysDef(defId);

        addClickLog(BpmConstants.DEF_ID_BPM_DEPT_DELETE_PAGE_WIDGET,desc, loginUser, sysDef, clickValue);
    }

    @Override
    public PageBean queryRecord(String startDate, String endDate, String keyWord, Integer pageNo, Integer pageSize) {
        PageBean pageBean = new PageBean();
        List<DeptRecordVo> list = new ArrayList<>();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDefClickLog__defType.dot(DFN.sysDefClickLog_defTypeId), SysDefTypeCodeName.BPM_DEPT.getId()));
        if (StringUtil.isNotNull(startDate) && StringUtil.isNotNull(endDate)) {
            conds.add(new DC_B(DFN.sysDefClickLog__clickTime, DateUtil.parseDate(startDate,DateUtil.DATE_FORMAT)
                    ,DateUtil.getLastDayByDate(DateUtil.parseDate(endDate,DateUtil.DATE_FORMAT))));
        }
        if (StringUtil.isNotNull(keyWord)) {
            conds.add(new DC_R(DFN.sysDefClickLog__clickValue, keyWord));
        }
        Sort sort = Sort.by(Sort.Direction.DESC, DFN.sysDefClickLog__clickTime.n());
        Pager pager = new Pager();
        if (pageNo != null && pageSize != null) {
            pager.setSize(pageSize);
            pager.setIndex(pageNo);
        }
        long count = sysDefClickLogDao.countByConds(conds);
        pageBean.setCount((int) count);
        if (count > 0) {
            List<TeSysDefClickLog> defClickLogs = sysDefClickLogDao.findByConds(conds, sort, pager);
            pageBean.setCount(defClickLogs.size());
            for (TeSysDefClickLog defClickLog : defClickLogs) {
                DeptRecordVo vo = new DeptRecordVo();
                vo.setId(defClickLog.getId());
                vo.setOptionUser(defClickLog.getClickUser().getUserName()+"/"+defClickLog.getClickUser().getLoginName());
                vo.setOptionTime(defClickLog.getClickTime());
                vo.setOptionType(defClickLog.getPageWidget().getName());
                vo.setUpdateDesc(defClickLog.getClickValue());
                vo.setDesc(defClickLog.getDesc());
                vo.setDept(defClickLog.getDef().getName());
                list.add(vo);
            }
        }
        pageBean.setObjectList(list);
        return pageBean;
    }

    @Override
    public List<Map<String, Object>> exportRecord(String startDate, String endDate, String keyWord) {
        List<Map<String, Object>> dataMap = new ArrayList<>();
        PageBean pageBean = queryRecord(startDate, endDate, keyWord, null, null);
        List<DeptRecordVo> list = pageBean.getObjectList();
        int no = 0;
        for (DeptRecordVo vo : list) {
            no++;
            Map<String, Object> map = BeanMapUtils.beanToMap(vo);
            map.put("no",no);

            dataMap.add(map);
        }
        return dataMap;
    }

    @Override
    public List<DeptVo> queryDept(ObjectId defId,TeSysUser sysUser) {
        List<DeptVo> list = new ArrayList<>();
        // 1、取出所有bpm部门
        List<SysDef> bpmDepts = sysDefService.getSysDefsByDefType(SysDefTypeCodeName.BPM_DEPT);
        List<ObjectId> bpmDeptIds = bpmDepts.stream().map(SysDef::getId).collect(Collectors.toList());

        // 2、取出部门下所有的负责人清单
        List<ObjectId> roleIds = new ArrayList<>();
        roleIds.add(BpmConstants.DEF_ID_ROLE_RESP_ID);
        List<TeSysDefRoleUser> defRoleUsers = sysDefRoleUserService.getUsers(roleIds, bpmDeptIds,
                SysDefTypeCodeName.BPM_DEPT);
        Map<ObjectId, TeSysDefRoleUser2User> deptRespMap = defRoleUsers.stream().collect(Collectors.toMap(TeSysDefRoleUser::getDefId, TeSysDefRoleUser::getRoleUser));

        Map<ObjectId, SysDef> deptMap = bpmDepts.stream().collect(Collectors.toMap(SysDef::getId, d -> d));

        PermList permList = bpmApplyService.getPermLists(sysUser.getLoginName(), sysUser, defId);
        List<TreeNode> deptTrees = permList.getDeptTrees();
        int no = 0;
        for (TreeNode deptTree : deptTrees) {
            no++;
            SysDef def = deptMap.get(deptTree.getValue());
            String parentDefPath = def.getParentDefPath();
            String[] split = parentDefPath.split(",");
            DeptVo vo = new DeptVo();
            vo.setId(deptTree.getValue());
            vo.setNo(String.valueOf(no));
            setVo(def,split,vo,deptMap,deptRespMap);
            list.add(vo);
            List<TreeNode> children = deptTree.getChildren();
            if (CollectionUtils.isEmpty(children)) {
                continue;
            }
            int no2 = 0;
            for (TreeNode child : children) {
                no2++;
                SysDef def2 = deptMap.get(child.getValue());
                String parentDefPath2 = def2.getParentDefPath();
                String[] split2 = parentDefPath2.split(",");
                DeptVo secondVo = new DeptVo();
                secondVo.setId(child.getValue());
                secondVo.setNo(no+"."+no2);
                setVo(def2,split2,secondVo,deptMap,deptRespMap);
                list.add(secondVo);
                List<TreeNode> third = child.getChildren();
                if (CollectionUtils.isEmpty(third)) {
                    continue;
                }
                int no3 = 0;
                for (TreeNode treeNode : third) {
                    no3++;
                    SysDef def3 = deptMap.get(treeNode.getValue());
                    String parentDefPath3 = def3.getParentDefPath();
                    String[] split3 = parentDefPath3.split(",");
                    DeptVo thirdVo = new DeptVo();
                    thirdVo.setId(treeNode.getValue());
                    thirdVo.setNo(no+"."+no2+"."+no3);
                    setVo(def3,split3,thirdVo,deptMap,deptRespMap);
                    list.add(thirdVo);
                }
            }
        }

        return list;
    }

    private void setVo(SysDef deptDef, String[] split, DeptVo vo, Map<ObjectId, SysDef> deptMap, Map<ObjectId, TeSysDefRoleUser2User> deptRespMap) {
        if (split.length == 2){
            vo.setDivision(deptDef.getDefName());
            vo.setDesc(deptDef.getDefDesc());
            vo.setCodeName(deptDef.getCodeName());
        }
        if (split.length == 3){
            SysDef division = deptMap.get(StringUtil.toObjectId(split[2]));
            vo.setDivision(division.getDefName());

            vo.setVirtualCenter(deptDef.getDefName());
            vo.setDesc(deptDef.getDefDesc());
        }
        if (split.length == 4){
            SysDef division = deptMap.get(StringUtil.toObjectId(split[2]));
            vo.setDivision(division.getDefName());

            SysDef virtualCenter = deptMap.get(StringUtil.toObjectId(split[3]));
            vo.setVirtualCenter(virtualCenter.getDefName());

            vo.setDept(deptDef.getDefName());
            vo.setDesc(deptDef.getDefDesc());
        }
        vo.setDefNo(deptDef.getDefNo());
        TeSysDefRoleUser2User user = deptRespMap.get(deptDef.getId());
        if (user != null){
            vo.setResp(user.getUserName()+"/"+user.getLoginName());
        }
        vo.setDesc(deptDef.getDefDesc());
    }

    @Override
    public List<Map<String, Object>> exportDept(ObjectId defId, TeSysUser sysUser) {
        List<Map<String, Object>> dataMap = new ArrayList<>();
        List<DeptVo> list = queryDept(defId, sysUser);
        for (DeptVo vo : list) {
            Map<String, Object> map = BeanMapUtils.beanToMap(vo);

            dataMap.add(map);
        }
        return dataMap;
    }

    private void addClickLog(ObjectId pageWidgetId,String desc, TeSysUser loginUser, TeSysDef sysDef, String clickValue) {
        TeSysDefClickLog log = new TeSysDefClickLog() ;

        TeSysDefClickLog2DefType logDefType = new TeSysDefClickLog2DefType() ;
        TeDefType defType = sysDef.getDefType();
        logDefType.setDefTypeCodeName(defType.getDefTypeCodeName()) ;
        logDefType.setDefTypeId(defType.getDefTypeId()) ;
        logDefType.setDefTypeName(defType.getDefTypeName()) ;

        TeIdNameCn def =new TeIdNameCn();
        def.setCid(sysDef.getId());
        def.setName(sysDef.getDefName());
        log.setDef(def);
        log.setDefType(logDefType) ;
        SysDef page = sysDefService.getSysDefById(BpmConstants.DEF_ID_BPM_DEPT_EDIT);
        SysDef pageWidget = sysDefService.getSysDefById(pageWidgetId);
        log.setPage(page.trans2IdName()) ;
        log.setPageWidget(pageWidget.trans2IdNameCn());
        log.setClickTime(new Date()) ;

        TeSysDefClickLog2User clickUser = new TeSysDefClickLog2User() ;
        clickUser.setJobCode(loginUser.getJobCode()) ;
        clickUser.setUserId(loginUser.getId()) ;
        clickUser.setLoginName(loginUser.getLoginName()) ;
        clickUser.setUserName(loginUser.getUserName()) ;
        log.setClickUser(clickUser) ;
        log.setClickValue(clickValue);
        log.setDesc(desc);

        sysDefClickLogService.addTeSysDefClickLog(log);
    }
}
