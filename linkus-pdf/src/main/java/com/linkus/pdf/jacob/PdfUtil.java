package com.linkus.pdf.jacob;

import com.jacob.activeX.ActiveXComponent;
import com.jacob.com.ComThread;
import com.jacob.com.Dispatch;
import com.jacob.com.Variant;
import com.linkus.pdf.uitl.OfficeFileValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.rmi.ServerException;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Office文件转PDF工具类
 * 支持Word、Excel、PowerPoint文件转换为PDF格式
 */
@Component
@Slf4j
public class PdfUtil {

    /**
     * word 转换为 pdf 的格式宏，值为 17
     */
    private static final int WORD_FORMAT_PDF = 17;
    /**
     * excel转pdf格式
     */
    private static final int EXCEL_TYPE_PDF = 0;
    /**
     * ppt转pdf格式
     */
    private static final int PPT_SAVE_AS_PDF = 32;

    @Value("${pdf.conversion.timeout:120}")
    private int conversionTimeoutSeconds;

    @Value("${pdf.conversion.thread-pool.core-size:2}")
    private int threadPoolCoreSize;

    @Value("${pdf.conversion.thread-pool.max-size:5}")
    private int threadPoolMaxSize;

    @Value("${pdf.conversion.thread-pool.queue-capacity:10}")
    private int threadPoolQueueCapacity;

    private ThreadPoolTaskExecutor taskExecutor;

    // 用于跟踪活跃任务数量
    private final AtomicInteger activeTaskCount = new AtomicInteger(0);

    /**
     * 初始化线程池
     */
    @PostConstruct
    public void init() {
        taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(threadPoolCoreSize);
        taskExecutor.setMaxPoolSize(threadPoolMaxSize);
        taskExecutor.setQueueCapacity(threadPoolQueueCapacity);
        taskExecutor.setThreadNamePrefix("pdf-conversion-");
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        taskExecutor.initialize();
        log.info("PDF转换线程池初始化完成，核心线程数：{}，最大线程数：{}，队列容量：{}",
                threadPoolCoreSize, threadPoolMaxSize, threadPoolQueueCapacity);
    }

    /**
     * 应用关闭时清理线程池资源
     * 等待所有活跃任务完成，确保资源正确释放
     */
    @PreDestroy
    public void destroy() {
        if (taskExecutor != null) {
            int currentActiveCount = activeTaskCount.get();
            log.info("正在关闭PDF转换线程池，当前活跃任务数: {}", currentActiveCount);

            // 等待活跃任务完成
            if (currentActiveCount > 0) {
                log.info("等待{}个活跃任务完成...", currentActiveCount);
                long startTime = System.currentTimeMillis();
                while (activeTaskCount.get() > 0 && (System.currentTimeMillis() - startTime) < 60000) {
                    try {
                        Thread.sleep(1000);
                        int remaining = activeTaskCount.get();
                        if (remaining > 0) {
                            log.debug("仍有{}个任务在执行中...", remaining);
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("等待任务完成时被中断", e);
                        break;
                    }
                }

                int finalActiveCount = activeTaskCount.get();
                if (finalActiveCount > 0) {
                    log.warn("仍有{}个任务未完成，强制关闭线程池", finalActiveCount);
                } else {
                    log.info("所有活跃任务已完成");
                }
            }

            taskExecutor.shutdown();
            try {
                if (!taskExecutor.getThreadPoolExecutor().awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("线程池未能在30秒内正常关闭，强制关闭");
                    taskExecutor.getThreadPoolExecutor().shutdownNow();
                    if (!taskExecutor.getThreadPoolExecutor().awaitTermination(30, TimeUnit.SECONDS)) {
                        log.error("PDF转换线程池无法正常关闭");
                    }
                }
            } catch (InterruptedException e) {
                taskExecutor.getThreadPoolExecutor().shutdownNow();
                Thread.currentThread().interrupt();
                log.error("关闭PDF转换线程池时被中断", e);
            }
            log.info("PDF转换线程池已关闭");
        }
    }

    /**
     * Office文件转PDF
     *
     * @param inputFile 输入文件路径
     * @param pdfFile   输出PDF文件路径
     * @param checkFile 是否进行文件预检查
     * @throws ServerException 转换失败时抛出异常
     */
    public void officeConvertPdf(String inputFile, String pdfFile, boolean checkFile) throws ServerException {
        // 参数验证
        validateParameters(inputFile, pdfFile);

        File inputFileObj = new File(inputFile);

        // 文件存在性检查
        if (!inputFileObj.exists()) {
            throw new ServerException("输入文件不存在: " + inputFile);
        }

        if (!inputFileObj.canRead()) {
            throw new ServerException("输入文件无法读取: " + inputFile);
        }

        // 创建输出目录
        createOutputDirectory(pdfFile);

        // 文件预检查
        if (checkFile && !OfficeFileValidator.officeValid(inputFileObj)) {
            throw new ServerException("文件预检查失败，文件可能损坏或格式不支持");
        }

        String fileExtension = getFileSuffix(inputFile);
        log.info("开始转换文件: {} -> {}, 文件类型: {}", inputFile, pdfFile, fileExtension);

        try {
            switch (fileExtension.toLowerCase()) {
                case "doc":
                case "docx":
                    wordToPDF(inputFile, pdfFile);
                    break;
                case "ppt":
                case "pptx":
                    pptToPDF(inputFile, pdfFile);
                    break;
                case "xls":
                case "xlsx":
                    excelToPDF(inputFile, pdfFile);
                    break;
                case "pdf":
                    throw new ServerException("PDF格式不需要转换");
                default:
                    throw new ServerException("不支持的文件格式: " + fileExtension);
            }
            log.info("文件转换成功: {} -> {}", inputFile, pdfFile);
        } catch (ServerException e) {
            log.error("文件转换失败: {} -> {}, 错误: {}", inputFile, pdfFile, e.getMessage());
            throw e;
        }
    }

    /**
     * 参数验证
     */
    private void validateParameters(String inputFile, String pdfFile) throws ServerException {
        if (!StringUtils.hasText(inputFile)) {
            throw new ServerException("输入文件路径不能为空");
        }
        if (!StringUtils.hasText(pdfFile)) {
            throw new ServerException("输出文件路径不能为空");
        }
    }

    /**
     * 创建输出目录
     */
    private void createOutputDirectory(String pdfFile) throws ServerException {
        try {
            Path outputPath = Paths.get(pdfFile).getParent();
            if (outputPath != null && !Files.exists(outputPath)) {
                Files.createDirectories(outputPath);
                log.debug("创建输出目录: {}", outputPath);
            }
        } catch (Exception e) {
            throw new ServerException("创建输出目录失败: " + e.getMessage());
        }
    }

    /**
     * 通过文件后缀解析文件类型
     *
     * @param fileName 文件名
     * @return 文件后缀名，如果没有后缀则返回空字符串
     */
    public static String getFileSuffix(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return "";
        }
        int lastIndexOf = fileName.lastIndexOf(".");
        return lastIndexOf != -1 ? fileName.substring(lastIndexOf + 1) : "";
    }

    /**
     * Word转PDF
     *
     * @param inputFile 输入Word文件路径
     * @param pdfFile   输出PDF文件路径
     * @throws ServerException 转换失败时抛出异常
     */
    private void wordToPDF(String inputFile, String pdfFile) throws ServerException {
        executeConversion(new WordConverter(inputFile, pdfFile), "Word");
    }

    /**
     * PPT转PDF
     *
     * @param inputFile 输入PPT文件路径
     * @param pdfFile   输出PDF文件路径
     * @throws ServerException 转换失败时抛出异常
     */
    private void pptToPDF(String inputFile, String pdfFile) throws ServerException {
        executeConversion(new PptConverter(inputFile, pdfFile), "PPT");
    }

    /**
     * Excel转PDF
     *
     * @param inputFile 输入Excel文件路径
     * @param pdfFile   输出PDF文件路径
     * @throws ServerException 转换失败时抛出异常
     */
    private void excelToPDF(String inputFile, String pdfFile) throws ServerException {
        executeConversion(new ExcelConverter(inputFile, pdfFile), "Excel");
    }

    /**
     * 执行转换任务的通用方法
     */
    private void executeConversion(Callable<Void> converter, String fileType) throws ServerException {
        int currentActiveCount = activeTaskCount.incrementAndGet();
        log.debug("开始执行{}转换任务，当前活跃任务数: {}", fileType, currentActiveCount);

        try {
            Future<Void> future = taskExecutor.submit(converter);
            future.get(conversionTimeoutSeconds, TimeUnit.SECONDS);
            log.debug("{}转换任务完成", fileType);
        } catch (TimeoutException e) {
            log.error("{}转PDF超时，超时时间: {}秒，当前活跃任务数: {}", fileType, conversionTimeoutSeconds, currentActiveCount, e);
            throw new ServerException(fileType + "转PDF超时");
        } catch (ExecutionException e) {
            log.error("{}转PDF过程中发生异常，当前活跃任务数: {}", fileType, currentActiveCount, e);
            Throwable cause = e.getCause();
            if (cause instanceof ServerException) {
                throw (ServerException) cause;
            }
            throw new ServerException(fileType + "转PDF失败: " + cause.getMessage());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("{}转PDF过程被中断，当前活跃任务数: {}", fileType, currentActiveCount, e);
            throw new ServerException(fileType + "转PDF被中断");
        } finally {
            int remainingCount = activeTaskCount.decrementAndGet();
            log.debug("{}转换任务结束，剩余活跃任务数: {}", fileType, remainingCount);
        }
    }


    /**
     * 转换器抽象基类
     */
    abstract static class BaseConverter implements Callable<Void> {
        protected final String inputFile;
        protected final String pdfFile;
        private final String taskId;

        BaseConverter(String inputFile, String pdfFile) {
            this.inputFile = inputFile;
            this.pdfFile = pdfFile;
            this.taskId = Thread.currentThread().getName() + "-" + System.currentTimeMillis();
        }

        @Override
        public Void call() throws Exception {
            // 使用线程本地的COM初始化，避免多线程冲突
            boolean comInitialized = false;
            ActiveXComponent app = null;
            Dispatch collections = null;
            Dispatch document = null;

            try {
                log.debug("[{}] 开始{}转换: {} -> {}", taskId, getFileType(), inputFile, pdfFile);

                // 初始化COM线程
                ComThread.InitSTA();
                comInitialized = true;
                log.debug("[{}] COM线程初始化完成", taskId);

                // 创建应用程序实例（每个任务独立的实例）
                app = createApplication();
                log.debug("[{}] {}应用程序创建完成", taskId, getFileType());

                collections = getDocumentCollection(app);
                document = openDocument(collections);

                if (document == null) {
                    throw new ServerException("无法打开" + getFileType() + "文件，可能文件损坏或格式不支持");
                }

                log.debug("[{}] {}文档打开成功，开始转换", taskId, getFileType());
                performConversion(document);
                log.debug("[{}] {}转换完成: {} -> {}", taskId, getFileType(), inputFile, pdfFile);
                return null;
            } catch (Exception e) {
                log.error("[{}] {}转PDF过程中发生异常: {}", taskId, getFileType(), e.getMessage(), e);
                throw new ServerException(getFileType() + "转PDF失败: " + e.getMessage());
            } finally {
                // 安全释放资源，确保每个任务的资源独立释放
                safeRelease(app, collections, document, getFileType(), taskId, comInitialized);
            }
        }

        protected abstract String getFileType();
        protected abstract ActiveXComponent createApplication();
        protected abstract Dispatch getDocumentCollection(ActiveXComponent app);
        protected abstract Dispatch openDocument(Dispatch collections);
        protected abstract void performConversion(Dispatch document);
    }

    /**
     * Word转换器
     */
    static class WordConverter extends BaseConverter {
        WordConverter(String inputFile, String pdfFile) {
            super(inputFile, pdfFile);
        }

        @Override
        protected String getFileType() {
            return "Word";
        }

        @Override
        protected ActiveXComponent createApplication() {
            ActiveXComponent app = new ActiveXComponent("KWPS.Application");
            app.setProperty("Visible", new Variant(false));
            app.setProperty("AutomationSecurity", new Variant(3));
            return app;
        }

        @Override
        protected Dispatch getDocumentCollection(ActiveXComponent app) {
            return app.getProperty("Documents").toDispatch();
        }

        @Override
        protected Dispatch openDocument(Dispatch collections) {
            return Dispatch.call(collections, "Open", inputFile, false, true).toDispatch();
        }

        @Override
        protected void performConversion(Dispatch document) {
            // 清理文档属性
            Dispatch.call(document, "DeleteAllComments");
            Dispatch.call(document, "RemoveDocumentInformation", 14);
            // 导出为PDF
            Dispatch.call(document, "ExportAsFixedFormat", pdfFile, WORD_FORMAT_PDF);
        }
    }

    /**
     * PPT转换器
     */
    static class PptConverter extends BaseConverter {
        PptConverter(String inputFile, String pdfFile) {
            super(inputFile, pdfFile);
        }

        @Override
        protected String getFileType() {
            return "PPT";
        }

        @Override
        protected ActiveXComponent createApplication() {
            ActiveXComponent app = new ActiveXComponent("KWPP.Application");
            app.setProperty("Visible", new Variant(false));
            app.setProperty("AutomationSecurity", new Variant(3));
            return app;
        }

        @Override
        protected Dispatch getDocumentCollection(ActiveXComponent app) {
            return app.getProperty("Presentations").toDispatch();
        }

        @Override
        protected Dispatch openDocument(Dispatch collections) {
            return Dispatch.call(collections, "Open", inputFile, true, true, false).toDispatch();
        }

        @Override
        protected void performConversion(Dispatch document) {
            Dispatch.call(document, "SaveAs", pdfFile, PPT_SAVE_AS_PDF);
        }
    }

    /**
     * Excel转换器
     */
    static class ExcelConverter extends BaseConverter {
        ExcelConverter(String inputFile, String pdfFile) {
            super(inputFile, pdfFile);
        }

        @Override
        protected String getFileType() {
            return "Excel";
        }

        @Override
        protected ActiveXComponent createApplication() {
            ActiveXComponent app = new ActiveXComponent("KET.Application");
            app.setProperty("Visible", false);
            app.setProperty("AutomationSecurity", new Variant(3));
            return app;
        }

        @Override
        protected Dispatch getDocumentCollection(ActiveXComponent app) {
            return app.getProperty("Workbooks").toDispatch();
        }

        @Override
        protected Dispatch openDocument(Dispatch collections) {
            return Dispatch.call(collections, "Open", inputFile, false, true).toDispatch();
        }

        @Override
        protected void performConversion(Dispatch document) {
            Dispatch.invoke(document, "ExportAsFixedFormat", Dispatch.Method,
                    new Object[]{new Variant(0), pdfFile, new Variant(EXCEL_TYPE_PDF)}, new int[1]);
        }
    }

    /**
     * 安全释放COM组件资源
     * 每个任务独立释放自己的资源，避免多线程冲突
     *
     * @param app 应用程序组件
     * @param collections 文档集合
     * @param document 文档对象
     * @param appType 应用程序类型
     * @param taskId 任务ID
     * @param comInitialized COM是否已初始化
     */
    private static void safeRelease(ActiveXComponent app, Dispatch collections, Dispatch document,
                                   String appType, String taskId, boolean comInitialized) {
        log.debug("[{}] 开始释放{}资源", taskId, appType);

        // 1. 首先关闭文档（最重要的资源）
        if (Objects.nonNull(document)) {
            try {
                log.debug("[{}] 关闭{}文档", taskId, appType);
                if ("PPT".equals(appType)) {
                    // PPT文档关闭方式不同
                    Dispatch.call(document, "Close");
                } else {
                    // Word和Excel文档关闭方式
                    Dispatch.call(document, "Close", false);
                }
                document.safeRelease();
                log.debug("[{}] {}文档已关闭", taskId, appType);
            } catch (Exception e) {
                log.warn("[{}] 关闭{}文档时发生异常: {}", taskId, appType, e.getMessage(), e);
            }
        }

        // 2. 释放文档集合
        if (Objects.nonNull(collections)) {
            try {
                collections.safeRelease();
                log.debug("[{}] {}文档集合已释放", taskId, appType);
            } catch (Exception e) {
                log.warn("[{}] 释放{}文档集合时发生异常: {}", taskId, appType, e.getMessage(), e);
            }
        }

        // 3. 关闭应用程序实例（每个任务有独立的实例，不会影响其他任务）
        if (Objects.nonNull(app)) {
            try {
                log.debug("[{}] 关闭{}应用程序实例", taskId, appType);
                // 使用更温和的方式关闭，避免强制退出影响其他实例
                app.invoke("Quit", new Variant[]{});
                app.safeRelease();
                log.debug("[{}] {}应用程序实例已关闭", taskId, appType);
            } catch (Exception e) {
                log.warn("[{}] 关闭{}应用程序实例时发生异常: {}", taskId, appType, e.getMessage(), e);
            }
        }

        // 4. 释放COM线程（只释放当前线程的COM资源）
        if (comInitialized) {
            try {
                log.debug("[{}] 释放{}COM线程", taskId, appType);
                ComThread.Release();
                log.debug("[{}] {}COM线程已释放", taskId, appType);
            } catch (Exception e) {
                log.warn("[{}] 释放{}COM线程时发生异常: {}", taskId, appType, e.getMessage(), e);
            }
        }

        log.debug("[{}] {}资源释放完成", taskId, appType);
    }
}
