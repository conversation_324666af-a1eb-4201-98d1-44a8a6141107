package com.linkus.pdf.ctrl;

import com.linkus.pdf.service.IPDFConvertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.rmi.ServerException;

/**
 * PDf转换类
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/file/convert")
public class PDFConvertCtrl {
    @Resource
    private IPDFConvertService pdfConvertService;

    /**
     * 文件转换PDF
     *
     * @param file 上传的文件
     * @return 转换后的PDF文件内容
     */
    @PostMapping(value = "/pdf", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<org.springframework.core.io.Resource> officeConvertPdf(
            @RequestPart("file") MultipartFile file,
            @RequestParam(value = "checkFile", defaultValue = "true") boolean checkFile
    ) {

        String originalFilename = file.getOriginalFilename();
        long start = System.currentTimeMillis();

        try {
            if (StringUtils.isEmpty(originalFilename)) {
                throw new ServerException("文件名不能为空");
            }
            log.info("文件转换开始：{}", originalFilename);

            org.springframework.core.io.Resource pdfResource = pdfConvertService.convert(file, checkFile);
            if (pdfResource == null || !pdfResource.exists()) {
                throw new ServerException("转换后文件不存在：" + originalFilename);
            }

            String pdfFilename = originalFilename.substring(0, originalFilename.lastIndexOf(".")) + ".pdf";
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + pdfFilename + "\"")
                    .body(pdfResource);

        } catch (Exception ex) {

            log.error("文件转换异常：{}", originalFilename, ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(null);

        } finally {
            log.info("文件转换结束[{}ms]：{}", System.currentTimeMillis() - start, originalFilename);
        }
    }
}
