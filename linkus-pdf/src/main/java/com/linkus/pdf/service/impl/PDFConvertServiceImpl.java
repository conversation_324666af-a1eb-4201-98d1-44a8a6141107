package com.linkus.pdf.service.impl;

import com.linkus.pdf.service.IPDFConvertService;
import com.linkus.pdf.jacob.PdfUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.rmi.ServerException;
import java.util.UUID;


@Service
@Slf4j
public class PDFConvertServiceImpl implements IPDFConvertService {

    @Value("${outputBasePath}")
    private String transferPath;

    @Override
    public Resource convert(MultipartFile file, boolean checkFile) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new ServerException("文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            throw new ServerException("文件名不能为空");
        }
        String fileExtension = PdfUtil.getFileSuffix(fileName);
        if (StringUtils.isEmpty(fileExtension)) {
            throw new ServerException("文件扩展名不能为空");
        }

        String path = transferPath + UUID.randomUUID() + File.separator;
        String inputPath = path + "input." + fileExtension;
        String pdfOutputPath = path + "output.pdf";
        log.info("fileName: {}, path：{}, inputPath: {}, pdfOutputPath: {}", fileName, path, inputPath, pdfOutputPath);

        // 将上传的文件保存到临时目录
        File files = new File(path);
        if (!files.exists() && !files.mkdirs()) {
            throw new ServerException("创建临时目录失败");
        }
        file.transferTo(new File(inputPath));

        PdfUtil.officeConvertPdf(inputPath, pdfOutputPath, checkFile);

        // 返回转换后的PDF文件资源
        // 注意：这里返回的是临时文件资源，调用方需要负责清理
        // 不要在这里删除文件，因为资源还需要被使用
        File pdfFile = new File(pdfOutputPath);
        return new FileSystemResource(pdfFile);
    }
}
