package com.linkus.pdf.jacob;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.IOException;
import java.rmi.ServerException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PdfUtil测试类
 */
@ExtendWith(MockitoExtension.class)
class PdfUtilTest {

    @InjectMocks
    private PdfUtil pdfUtil;

    @BeforeEach
    void setUp() {
        // 设置配置属性
        ReflectionTestUtils.setField(pdfUtil, "conversionTimeoutSeconds", 120);
        ReflectionTestUtils.setField(pdfUtil, "threadPoolCoreSize", 2);
        ReflectionTestUtils.setField(pdfUtil, "threadPoolMaxSize", 5);
        ReflectionTestUtils.setField(pdfUtil, "threadPoolQueueCapacity", 10);
        
        // 初始化线程池
        pdfUtil.init();
    }

    @Test
    void testValidateParameters_NullInputFile() {
        ServerException exception = assertThrows(ServerException.class, () -> {
            pdfUtil.officeConvertPdf(null, "output.pdf", false);
        });
        assertEquals("输入文件路径不能为空", exception.getMessage());
    }

    @Test
    void testValidateParameters_EmptyInputFile() {
        ServerException exception = assertThrows(ServerException.class, () -> {
            pdfUtil.officeConvertPdf("", "output.pdf", false);
        });
        assertEquals("输入文件路径不能为空", exception.getMessage());
    }

    @Test
    void testValidateParameters_NullOutputFile() {
        ServerException exception = assertThrows(ServerException.class, () -> {
            pdfUtil.officeConvertPdf("input.docx", null, false);
        });
        assertEquals("输出文件路径不能为空", exception.getMessage());
    }

    @Test
    void testValidateParameters_EmptyOutputFile() {
        ServerException exception = assertThrows(ServerException.class, () -> {
            pdfUtil.officeConvertPdf("input.docx", "", false);
        });
        assertEquals("输出文件路径不能为空", exception.getMessage());
    }

    @Test
    void testFileNotExists() {
        ServerException exception = assertThrows(ServerException.class, () -> {
            pdfUtil.officeConvertPdf("nonexistent.docx", "output.pdf", false);
        });
        assertTrue(exception.getMessage().contains("输入文件不存在"));
    }

    @Test
    void testPdfFileNoConversion() throws IOException {
        // 创建临时PDF文件
        File tempFile = File.createTempFile("test", ".pdf");
        tempFile.deleteOnExit();

        ServerException exception = assertThrows(ServerException.class, () -> {
            pdfUtil.officeConvertPdf(tempFile.getAbsolutePath(), "output.pdf", false);
        });
        assertEquals("PDF格式不需要转换", exception.getMessage());
    }

    @Test
    void testUnsupportedFileFormat() throws IOException {
        // 创建临时文件
        File tempFile = File.createTempFile("test", ".txt");
        tempFile.deleteOnExit();

        ServerException exception = assertThrows(ServerException.class, () -> {
            pdfUtil.officeConvertPdf(tempFile.getAbsolutePath(), "output.pdf", false);
        });
        assertTrue(exception.getMessage().contains("不支持的文件格式"));
    }

    @Test
    void testGetFileSuffix() {
        // 使用反射调用私有方法
        String suffix = (String) ReflectionTestUtils.invokeMethod(pdfUtil, "getFileSuffix", "test.docx");
        assertEquals("docx", suffix);

        suffix = (String) ReflectionTestUtils.invokeMethod(pdfUtil, "getFileSuffix", "test.pdf");
        assertEquals("pdf", suffix);

        suffix = (String) ReflectionTestUtils.invokeMethod(pdfUtil, "getFileSuffix", "test");
        assertEquals("", suffix);

        suffix = (String) ReflectionTestUtils.invokeMethod(pdfUtil, "getFileSuffix", "");
        assertEquals("", suffix);
    }
}
