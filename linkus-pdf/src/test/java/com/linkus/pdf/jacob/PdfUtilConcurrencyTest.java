package com.linkus.pdf.jacob;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PdfUtil并发测试类
 * 测试多线程环境下的安全性
 */
@ExtendWith(MockitoExtension.class)
class PdfUtilConcurrencyTest {

    @InjectMocks
    private PdfUtil pdfUtil;

    @BeforeEach
    void setUp() {
        // 设置配置属性
        ReflectionTestUtils.setField(pdfUtil, "conversionTimeoutSeconds", 120);
        ReflectionTestUtils.setField(pdfUtil, "threadPoolCoreSize", 3);
        ReflectionTestUtils.setField(pdfUtil, "threadPoolMaxSize", 5);
        ReflectionTestUtils.setField(pdfUtil, "threadPoolQueueCapacity", 10);
        
        // 初始化线程池
        pdfUtil.init();
    }

    @Test
    void testConcurrentParameterValidation() throws InterruptedException {
        int threadCount = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        for (int i = 0; i < threadCount; i++) {
            final int taskId = i;
            executor.submit(() -> {
                try {
                    // 测试并发参数验证
                    if (taskId % 2 == 0) {
                        // 一半的任务使用无效参数
                        pdfUtil.officeConvertPdf(null, "output" + taskId + ".pdf", false);
                    } else {
                        // 一半的任务使用不存在的文件
                        pdfUtil.officeConvertPdf("nonexistent" + taskId + ".docx", "output" + taskId + ".pdf", false);
                    }
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                    // 预期的异常，验证错误处理是否正确
                    assertTrue(e.getMessage().contains("输入文件路径不能为空") || 
                              e.getMessage().contains("输入文件不存在"));
                } finally {
                    latch.countDown();
                }
            });
        }

        assertTrue(latch.await(30, TimeUnit.SECONDS));
        assertEquals(0, successCount.get()); // 所有任务都应该失败
        assertEquals(threadCount, failureCount.get()); // 所有任务都应该抛出异常
    }

    @Test
    void testActiveTaskCountTracking() throws InterruptedException, IOException {
        // 创建临时PDF文件来触发"不需要转换"的异常
        File tempFile1 = File.createTempFile("test1", ".pdf");
        File tempFile2 = File.createTempFile("test2", ".pdf");
        tempFile1.deleteOnExit();
        tempFile2.deleteOnExit();

        int threadCount = 5;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger exceptionCount = new AtomicInteger(0);

        // 获取活跃任务计数器
        AtomicInteger activeTaskCount = (AtomicInteger) ReflectionTestUtils.getField(pdfUtil, "activeTaskCount");
        assertNotNull(activeTaskCount);
        assertEquals(0, activeTaskCount.get());

        for (int i = 0; i < threadCount; i++) {
            final int taskId = i;
            executor.submit(() -> {
                try {
                    File tempFile = (taskId % 2 == 0) ? tempFile1 : tempFile2;
                    pdfUtil.officeConvertPdf(tempFile.getAbsolutePath(), "output" + taskId + ".pdf", false);
                } catch (Exception e) {
                    exceptionCount.incrementAndGet();
                    assertTrue(e.getMessage().contains("PDF格式不需要转换"));
                } finally {
                    latch.countDown();
                }
            });
        }

        assertTrue(latch.await(30, TimeUnit.SECONDS));
        
        // 等待一小段时间确保所有任务完成
        Thread.sleep(1000);
        
        // 验证活跃任务计数器回到0
        assertEquals(0, activeTaskCount.get());
        assertEquals(threadCount, exceptionCount.get());
    }

    @Test
    void testThreadSafety() throws InterruptedException {
        int threadCount = 20;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger totalExceptions = new AtomicInteger(0);

        for (int i = 0; i < threadCount; i++) {
            final int taskId = i;
            executor.submit(() -> {
                try {
                    // 使用不同类型的无效输入来测试线程安全性
                    switch (taskId % 4) {
                        case 0:
                            pdfUtil.officeConvertPdf(null, "output.pdf", false);
                            break;
                        case 1:
                            pdfUtil.officeConvertPdf("", "output.pdf", false);
                            break;
                        case 2:
                            pdfUtil.officeConvertPdf("input.docx", null, false);
                            break;
                        case 3:
                            pdfUtil.officeConvertPdf("input.docx", "", false);
                            break;
                    }
                } catch (Exception e) {
                    totalExceptions.incrementAndGet();
                    // 验证异常消息的正确性
                    assertTrue(e.getMessage().contains("不能为空"));
                } finally {
                    latch.countDown();
                }
            });
        }

        assertTrue(latch.await(30, TimeUnit.SECONDS));
        assertEquals(threadCount, totalExceptions.get());
        
        // 验证活跃任务计数器
        AtomicInteger activeTaskCount = (AtomicInteger) ReflectionTestUtils.getField(pdfUtil, "activeTaskCount");
        assertEquals(0, activeTaskCount.get());
    }

    @Test
    void testGracefulShutdown() throws InterruptedException {
        AtomicInteger activeTaskCount = (AtomicInteger) ReflectionTestUtils.getField(pdfUtil, "activeTaskCount");
        assertEquals(0, activeTaskCount.get());

        // 模拟一些快速失败的任务
        ExecutorService executor = Executors.newFixedThreadPool(3);
        for (int i = 0; i < 3; i++) {
            executor.submit(() -> {
                try {
                    pdfUtil.officeConvertPdf("nonexistent.docx", "output.pdf", false);
                } catch (Exception e) {
                    // 预期的异常
                }
            });
        }

        // 等待任务完成
        Thread.sleep(2000);
        
        // 验证计数器正确
        assertEquals(0, activeTaskCount.get());

        // 测试优雅关闭
        pdfUtil.destroy();
        
        executor.shutdown();
        assertTrue(executor.awaitTermination(5, TimeUnit.SECONDS));
    }
}
