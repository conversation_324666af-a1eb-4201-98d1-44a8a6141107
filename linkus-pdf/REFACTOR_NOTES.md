# PdfUtil 重构说明

## 重构前的主要问题

1. **架构设计问题**
   - 类被标注为`@Component`但主要方法都是静态的
   - 静态线程池无法被Spring正确管理
   - 实例方法与静态方法混用

2. **资源管理问题**
   - 静态线程池在Spring容器重启时不会重新创建
   - 每种文件类型只有1个线程，可能成为性能瓶颈
   - `@PreDestroy`无法正确管理静态资源

3. **代码质量问题**
   - 异常处理过于宽泛
   - 硬编码的超时时间和线程池配置
   - 大量重复代码
   - 缺少输入参数验证
   - 日志记录过于详细

## 重构后的改进

### 1. 架构设计优化
- 统一为实例方法，正确使用Spring管理
- 使用Spring的`ThreadPoolTaskExecutor`替代静态线程池
- 添加`@PostConstruct`和`@PreDestroy`正确管理资源生命周期

### 2. 配置化改进
- 添加配置属性支持：
  - `pdf.conversion.timeout`: 转换超时时间
  - `pdf.conversion.thread-pool.core-size`: 核心线程数
  - `pdf.conversion.thread-pool.max-size`: 最大线程数
  - `pdf.conversion.thread-pool.queue-capacity`: 队列容量

### 3. 代码重构
- 创建`BaseConverter`抽象基类，减少重复代码
- 提取公共的转换逻辑到`executeConversion`方法
- 改进异常处理，更精确的异常捕获和处理

### 4. 参数验证增强
- 添加完整的输入参数验证
- 检查文件存在性和可读性
- 自动创建输出目录

### 5. 日志优化
- 将详细操作日志改为debug级别
- 优化异常日志格式
- 添加转换开始和完成的info日志

## 使用方式

### 配置文件
在`application.yml`或`application-pdf.yml`中添加：

```yaml
pdf:
  conversion:
    timeout: 120  # 转换超时时间（秒）
    thread-pool:
      core-size: 2      # 核心线程数
      max-size: 5       # 最大线程数
      queue-capacity: 10 # 队列容量
```

### 代码调用
```java
@Autowired
private PdfUtil pdfUtil;

public void convertToPdf() {
    try {
        pdfUtil.officeConvertPdf("input.docx", "output.pdf", true);
    } catch (ServerException e) {
        log.error("转换失败", e);
    }
}
```

## 测试
运行测试类`PdfUtilTest`来验证重构后的功能：
```bash
mvn test -Dtest=PdfUtilTest
```

## 多线程安全性改进

### 问题分析
原代码在多线程环境下存在以下风险：
1. **COM组件冲突**: 多个线程同时操作COM组件可能导致资源冲突
2. **应用程序实例共享**: 如果多个任务共享同一个Office应用程序实例，关闭操作会影响其他任务
3. **资源释放时机**: 在高并发情况下，资源释放可能影响正在执行的任务

### 解决方案
1. **独立的应用程序实例**: 每个转换任务创建独立的Office应用程序实例
2. **线程本地COM初始化**: 每个线程独立初始化和释放COM资源
3. **任务跟踪机制**: 使用AtomicInteger跟踪活跃任务数量
4. **优雅关闭**: 应用关闭时等待所有活跃任务完成
5. **详细的任务日志**: 每个任务有唯一ID，便于跟踪和调试

### 安全性保证
- ✅ 每个任务使用独立的Office应用程序实例
- ✅ COM线程资源独立管理，避免冲突
- ✅ 应用关闭时等待活跃任务完成
- ✅ 详细的错误处理和日志记录
- ✅ 资源释放异常不会影响其他任务

## 注意事项
1. 需要确保系统安装了相应的Office软件（WPS或Microsoft Office）
2. 在生产环境中建议调整线程池大小以适应实际负载
3. 可以根据需要调整转换超时时间
4. 建议在应用启动时预热线程池以提高首次转换性能
5. **多线程安全**: 现在支持高并发转换，每个任务使用独立的资源实例
6. **监控建议**: 可以通过日志监控活跃任务数量，及时发现性能瓶颈
