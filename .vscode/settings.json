{"java.format.settings.url": "", "java.format.settings.profile": "GoogleStyle", "java.format.enabled": true, "java.format.onType.enabled": false, "java.format.onPaste.enabled": false, "java.format.onSave.enabled": false, "java.completion.enabled": true, "java.completion.guessMethodArguments": false, "java.completion.favoriteStaticMembers": [], "java.completion.importOrder": ["java", "javax", "com", "org"], "java.saveActions.organizeImports": false, "java.codeGeneration.hashCodeEquals.useJava7Objects": true, "java.codeGeneration.useBlocks": true, "java.codeGeneration.generateComments": false, "java.codeGeneration.sortMembers": false, "java.maven.downloadSources": false, "java.maven.downloadJavadoc": false, "java.configuration.updateBuildConfiguration": "disabled", "java.autobuild.enabled": false, "java.maxConcurrentBuilds": 1, "files.exclude": {"**/target": true, "**/bin": true, "**/.classpath": true, "**/.project": true, "**/.settings": true, "**/.factorypath": true}, "search.exclude": {"**/target": true, "**/bin": true, "**/node_modules": true}, "files.watcherExclude": {"**/target/**": true, "**/bin/**": true}, "java.compile.nullAnalysis.mode": "automatic"}