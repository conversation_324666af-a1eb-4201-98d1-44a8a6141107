Vue.component('prj-header', {
    template:
    	'<div class="conrainer prjHeader header_menu" style="height:60px;min-width:1244px; position: relative;"> ' +
		'<div class="img_header">'+
	      '<div class="num-circle menu-fixed">'+
		        '{{loadTotalTodoTaskNum + todoRate.length + gradedUnGradeCount + gradedApplyCount + valueItemUnConfirmedCount + valueItemUnModifyCount + valueItemPmSelfAssessCount + valueItemAssessCount + myTodoApplyNum}}'+
	      '</div>'+
	      '<i-Menu mode="horizontal" theme="light" active-name="1" @on-select="changePage"   '+
		     'style="top:0;left:0;right:0;width:100%;padding:0 16px;min-width:1244px;">  '+
		     '<div class="layout-logo">'+
			   '<img src="/03images/logo1.png"/>'+
			   '<span>AI-PMS</span>'+
		     '</div>'+
		     '<div class="layout-nav" v-for="(menu, index) in menuList">'+
			   '<Submenu v-if="menu.hasSubMenu" :name="menu.id">'+
				  '<template slot="title">'+
					// '<i class="iconfont ivu-icon" :class="menu.icon" size="16"></i>'+
					'{{ menu.name }}'+
				  '</template>'+
				   '<Menu-Item v-for="subMenu in menu.subMenuList" :name="subMenu.id">'+
					  '<div v-if="subMenu.id == addBiz"> '+
						 '<i-Button class="ivu-btn ivu-btn-primary"   '+
							' style="padding-left:30px; padding-right:30px;"> '+
							' {{subMenu.name}} '+
						 '</i-Button>'+
					  '</div>'+
					  '<div v-else-if="subMenu.id == queryAll">'+
						    // '<Icon :type="subMenu.icon" size="18"></Icon> '+
						    '<span style="color:#bbbec4;">{{ subMenu.name }}</span> '+
					  '</div> '+
					  '<div v-else-if="subMenu.queryMenu==true || subMenu.queryBug==true || subMenu.pmAbility==true || subMenu.prjQualityReview==true">'+
						    '<span style="margin-left:10px;">{{ subMenu.name }}</span> '+
					  '</div> '+
					  '<div v-else-if="subMenu.id == findBug">'+
							'<span style="color:#bbbec4;">{{ subMenu.name }}</span> '+
					  '</div>'+
					  '<div v-else-if="subMenu.id == prjQualityReview">'+
						'<span style="color:#bbbec4;">{{ subMenu.name }}</span> '+
					 '</div>'+
					  '<div v-else>'+
						// '<Icon :type="subMenu.icon" size="16"></Icon>'+
						'{{ subMenu.name }}'+
					  '</div>'+
				     '</Menu-Item>'+
			      '</Submenu>'+
			      '<Menu-Item v-else-if="menu.id != other" :name="menu.id">'+
					// '<i class="iconfont ivu-icon" :class="menu.icon" size="16"></i>'+
				    '{{menu.name}}'+
			      '</Menu-Item>'+
			      '<Menu-Item v-else :name="menu.id" style="display:none;">'+
			      '</Menu-Item>'+
		       '</div>'+
		       '<div class="personal-center">' +


		'<Dropdown trigger="custom" placement="bottom-end" :visible="visible" @on-clickoutside="handleClose">'+
				        '<span style="font-size: 14px;" @click="handleOpen">'+
					       '{{ currentUser.userName }}'+
					       '<Icon type="ios-arrow-down" style="margin-left: 4px;"></Icon>'+
				        '</span>'+
				        '<Dropdown-Menu slot="list">'+
					         '<li class="ivu-menu-item">'+
						          '<div  @click="showTodoTasks">'+
							           '<i class="ivu-icon ivu-icon-clipboard" style="font-size: 18px;"></i>'+
							           '待我处理'+
						          '</div>'+
					         '</li> '+
					         '<li class="ivu-menu-item">'+
					 	          '<div @click="logout">'+
						 	          '<i class="ivu-icon ivu-icon-power" style="font-size: 18px;"></i>'+
							          '退出登录  '+
						          '</div>'+
					         '</li>'+
				        '</Dropdown-Menu>'+
			      '</Dropdown>'+
		     '</div>'+
	     '</i-Menu>'+
		'</div>'+
	     '<Modal v-model="prjInfoModal" transfer="true" title="新增项目" width="60%" class="win-input" :mask-closable="false" '+
			':styles="{top: 100}" @on-cancel=prjModalCancel>'+
			'<i-form ref="prjInfoForm" :model="prjInfo" style="margin-top:0px;" label-position="right" '+
				':rules="prjInfoValidate" :label-width="150">'+
				'<Form-Item label="项目编码：" prop="prjCode">'+
					'<i-input type="text" v-model="prjInfo.prjCode" placeholder="请输入项目编码"></i-input>'+
				'</Form-Item>'+
				'<Form-Item label="项目名称：" prop="prjName">'+
					'<i-input type="text" v-model="prjInfo.prjName" placeholder="请输入项目名称"></i-input>'+
				'</Form-Item>'+

				'<Form-Item label="项目经理：" prop="pmUser">'+
					'<lks-load-user-fuzzy :user.sync="prjInfo.pmUser"></lks-load-user-fuzzy>'+
				'</Form-Item>'+

				'<Form-Item label="项目管理员：" prop="prjAdmin">'+
					'<lks-load-user-fuzzy :user.sync="prjAdmin"></lks-load-user-fuzzy>'+
				'</Form-Item>'+
			'</i-form>'+

			'<div slot="footer">'+
				'<i-Button type="text" size="large" @click="prjModalCancel">取消</i-Button>'+
				'<i-Button type="primary" size="large" @click="prjModalOk">确定</i-Button>'+
			'</div>'+
		'</Modal>'+

		'<Modal v-model="createBizModal" transfer="true" :mask-closable="false" @on-cancel="cancelCreateBizModal" '+
		' class-name="vertical-center-modal embeded-iframe createBizPageNoFooter centerHeadModal" :width="800" style="margin-left:100px;margin-right:101px;"> '+
		' <div slot="header" style="height:22px;margin-top:5px;"> '+
			' <div class="ivu-modal-header-inner" style="width:25%;float:left;"> '+
				'<span>新增业务</span> '+
			'</div> '+
			'<div style="width:50%;text-align:center;float:left;"> '+
				'<a style="font-size:12px;text-decoration:underline;" href="#"  '+
					' @click="showBizView">{{createdBizViewCode}}</a> '+
			'</div> '+
			'<div style="width:25%;float:left;"> '+
				'<span></span> '+
			'</div> '+
		'</div> '+
		'<div style="height:450px;"> '+
			'<iframe id="crtBizIfrm" :src="createBizFrameSrc" style="width:100%;height:470px" frameborder="0"></iframe> '+
		'</div> '+
		'<div slot="footer"> '+
		'</div> '+
	  '</Modal>'+

	  '<div  :class="showDrawer?show:noshow" style="height: 0%; background-color: #f0f0f0;"> '+
		' <div class="drawer-mask" @click="hideTodoTasks" ></div> '+
		' <div class="drawer"> '+
			' <div class="num-circle tab-fixed" v-if="todoTasks && todoTasks.length != 0"> '+
				' {{ todoTasks ? todoTasks.length : 0 }} ' +
			' </div> '+
			' <div class="num-circle tab-fixed" style="left: 180px" v-if="todoRate && todoRate.length != 0"> '+
				' {{ todoRate ? todoRate.length : 0 }} '+
			' </div> '+
		    '<div class="type-select-wrap type-select-wrap-fixed">'+
				'<i-Select v-model="typeValue" @on-change="selectType" placeholder="请选择类型" transfer>'+
				'	<i-Option v-for="item in typeList" :value="item.value" :key="item.value">{{ item.label }}</i-Option>'+
				'</i-Select>'+
		    '</div>'+
			' <tabs value="name1"> '+
		'		<Spin class="todoTaskSpinClass" fix v-show="spinShow"></Spin>'+
				' <tab-pane label="待我审批" name="name1"> ' +
		'			<div class="todo_tasks_search_warp">' +
		'				<div v-if="!!typeValue && typeValue == \'prjBmk\'" class="level-select-wrap">'+
		'					<i-Select v-model="levelValues" @on-change="selectPrjLevel" placeholder="分类" clearable multiple filterable transfer>'+
		'						<i-Option v-for="item in levelList" :value="item.id" :key="item.id">{{ item.defName }}</i-Option>'+
		'					</i-Select>'+
		'				</div>'+
		'				<i-input :class="{showLevelInput: !!typeValue && typeValue == \'prjBmk\'}" v-model="todoTasksKey" placeholder="请输入关键字" :clearable="true"> <i slot="prefix" class="iconfont icon-search"></i></i-input>' +
		'			</div>'+
		'			<div class="line"></div> ' +
		'			<div class="todo_tasks_grad_tip" v-if="gradedUnGradeCount>0" @click="gotoGrad(\'unGrade\')">' +
		'				<div>项目分类管理 <tag type="border">待分类</tag></div>' +
		'				<div>项目数：{{gradedUnGradeCount}}</div>' +
		'			</div>'+
		'			<div class="line" v-if="gradedUnGradeCount>0"></div> '+
		'			<div class="todo_tasks_grad_tip" v-if="gradedApplyCount>0" @click="gotoGrad(\'toGrade\')">' +
		'				<div>项目分类管理  <tag type="border">待审批</tag></div>' +
		'				<div>项目数：{{gradedApplyCount}}</div>' +
		'			</div>'+
		'			<div class="line" v-if="gradedApplyCount>0"></div> '+

		'			<div class="todo_tasks_grad_tip" v-if="valueItemUnConfirmedCount + valueItemUnModifyCount + valueItemPmSelfAssessCount + valueItemAssessCount > 0" @click="goToValueItem">' +
		'				<div>价值交付确认&评估 <tag type="border">待处理</tag></div>' +
		'				<div>项目数：{{ valueItemUnConfirmedCount + valueItemUnModifyCount + valueItemPmSelfAssessCount + valueItemAssessCount }}</div>' +
		'			</div>'+
		'			<div class="line" v-if="valueItemUnConfirmedCount + valueItemUnModifyCount + valueItemPmSelfAssessCount + valueItemAssessCount > 0"></div> '+

		' 			<div v-for="task in todoTasks" style="cursor: pointer" @click="gotoApprove(task)" v-if="!task.isPending"> '+
						' <div class="drawer-content"> '+
							'<div class="prjname"> '+
								' <div style="width: 70%; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2;"> '+
									'{{task.name}} '+
								'</div> '+
								'<tag type="border">{{task.node.name}}</tag> '+
							'</div> '+
							' <div style="display: flex; justify-content: space-between; color: #B1B1B1"> '+
								'<div class="person">发起人：{{task.submitUserName}}</div> '+
								'<div class="time">发起时间：{{task.submitTime}}</div> '+
							' </div> '+
						'</div>	'+
						'<div class="line"></div> '+
					'</div> '+
					' <div v-for="task in todoTasks" style="cursor: pointer" @click="gotoRate(task)" v-if="task.isPending"> '+
						' <div class="drawer-content" style="height: 3.5em"> '+
							'<div class="prjname" style="height: 2em"> '+
								' <div style="width: 100%;  overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2;"> '+
								'{{task.def.codeName == "pmStakeholderAppraise" ? "项目经理能力模型项目维度评分" : "项目经理能力模型组织维度评分"}} '+
								'</div> '+
							'</div> '+
							'<div style="display: flex; justify-content: space-between; color: #B1B1B1"> '+
								'<div class="person">被评分人：{{task.emp?task.emp.userName:""}}</div> '+
								'<div class="time">发起时间：{{task.addTime?task.addTime.split(" ")[0]:""}}</div> '+
							'</div> '+
						'</div>	'+
					'<div class="line"></div> '+
					'</div> '+
					'<div v-if="tasksTipShow && !valueItemUnConfirmedCount + valueItemUnModifyCount + valueItemPmSelfAssessCount + valueItemAssessCount" style="text-align: center"><img src="../../03images/empty/no-data.png" alt="" style="width: 50%;margin: 50px"></div>'+
				'</tab-pane> '+

				' <tab-pane label="待我评分" name="name2" v-if="todoRate && todoRate.length != 0"> '+
					' <div v-for="task in todoRate" style="cursor: pointer" @click="gotoRate(task)"> '+
						' <div class="drawer-content" style="height: 3.5em"> '+
							'<div class="prjname" style="height: 2em"> '+
								' <div style="width: 100%;  overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2;"> '+
								'{{task.def.codeName == "prjDimAppraise" ? "项目经理能力模型项目维度评分" : "项目经理能力模型组织维度评分"}} '+
								'</div> '+
							'</div> '+
							' <div style="display: flex; justify-content: space-between; color: #B1B1B1"> '+
								'<div class="person">被评分人：{{task.emp?task.emp.userName:""}}</div> '+
								'<div class="time">发起时间：{{task.addTime?task.addTime.split(" ")[0]:""}}</div> '+
							' </div> '+
						'</div>	'+
						'<div class="line"></div> '+
					'</div> '+
				'</tab-pane> '+

		'</tabs> '+
		'</div> '+
	  '</div> '+
	  '<notice-show></notice-show>'+
    '</div>',
    //props: ['keyWord', 'isFuzzy'],
    data: function () {
    	  var sf = this;
		  //项目经理校验
		   var validatePm = function (rule, value, callback) {
			  if (!sf.prjInfo.pmUser || !sf.prjInfo.pmUser.userId) {
				   callback(new Error('请选择项目经理'));
			  } else {
				  callback();
			   }
		    };
		  //项目编码校验
		  var validatePrjCode = function (rule, value, callback) {
			  if (!sf.prjInfo.prjCode || sf.prjInfo.prjCode == null) {
				   callback(new Error('请填写项目编码'));
			  }else {
				  var reg = /^[0-9a-zA-Z]+$/;
				  if(!reg.test(sf.prjInfo.prjCode)){
					  callback(new Error('项目编码只能是数字或字母'));
				  }else{
					  callback();
				  }
			  }
		   };
    	return {

				valueItemUnConfirmedCount: 0,
				valueItemUnModifyCount: 0,
				valueItemPmSelfAssessCount: 0,
				valueItemAssessCount: 0,
			    myTodoApplyNum: 0,
			    loadTotalTodoTaskNum: 0,
			    spinShow: false,

			    typeList: [
					{
						value: "all",
						label: "全部"
					},
					{
						value: "prjBmk",
						label: "基准"
					},
					{
						value: "other",
						label: "其他"
					},
				],
				typeValue: "all",

				levelList: [],
				levelValues: [],

    		    loginUserSbuId: "",
    		    userAbpId: "601a16558006b628960c3228",
    		    power: false,
        	    100:'100px',
        	    show:   'drawer-wrapper bg-light-grey show',
        	    noshow: 'drawer-wrapper bg-light-grey',
        	    addBiz:'3-0',
        	    queryAll:'3-3',
				findBug:'9-7',
				rateId:'11-2',
        	    other:'-1',
				showContent: false,
				activeMenuId: '0-0',
				currentUser: {},
				menuList: [
					{
						id: '-1', load: false, display: false, hasSubMenu: false, name: '项目面板', icon: 'icon-xiangmumianban',
						url: linkus.location.rsc + '/02html/prj/prjDaskboard_real.html',
					},
					{
						id: '0-0', load: false, display: false, hasSubMenu: false, name: '项目面板', icon: 'icon-xiangmumianban',
						url: linkus.location.rsc + '/02html/prj/prjBudgetDashboard.html',
						subMenuList: [
							{
								id: '0-1', load: false, display: false, name: '项目面板-地图', icon: 'alert-circled',
								url: linkus.location.rsc + '/02html/prj/prjBudgetDashboard.html'
							},
							{
								id: '0-2', load: false, display: false, name: '数字化运营大屏', icon: 'alert-circled',
								url: 'http://mgr.devstone.cn:18080/dvdp/screen/1hZgYl'
							},
						]
					},
					/*{
						id: '0', load: false, display: false, name: '项目面板', hasSubMenu: true, icon: 'speedometer',
						subMenuList: [
							{
								id: '0-0', load: false, display: false, name: '项目面板', icon: 'ios-analytics',
								url: linkus.location.rsc + '/02html/prj/prjBudgetDashboard.html'
							},
							{
								id: '0-1', load: false, display: false, name: '业务面板', icon: 'connection-bars',
								url: linkus.location.rsc + '/02html/prj/prjDashborad_common.html?mode=view'
							}
						]
					},*/

					{
						id: '1', name: '项目信息', hasSubMenu: true, icon: 'icon-xiangmuxinxi',
						subMenuList: [
							{
								id: '1-1', load: false, display: false, name: '交施管理', icon: 'ios-paper',
								url: linkus.location.rsc + '/02html/prj/prjDeliveryImplementManagement.html'
							},
							/*{
								id: '1-2', load: false, display: false, name: '项目基准', icon: 'ios-paper',
								url: linkus.location.rsc + '/02html/prj/prjBasicInfo.html'
							},*/
							// {
							// 	id: '1-4', load: false, display: false, name: '项目预算', icon: 'ios-calculator',
							// 	url: linkus.location.rsc + '/02html/prj/new/prjBenchmarkBudgetRpt.html'
							// },
							/*{
								id: '1-8', load: false, display: false, name: '项目预算', icon: 'ios-calculator',
								url: linkus.location.rsc + '/02html/prj/prjBenchmarkBudgetRpt.html'
							},*/
							{
								id: '1-3', load: false, display: false, name: '项目集管理', icon: 'navicon-round',
								url: linkus.location.rsc + '/02html/prj/prjInfoManage.html'
							},
							{
								id: '1-7', load: false, display: false, name: '风险问题', icon: 'alert-circled',
								url: linkus.location.rsc + '/02html/prj/prjRiskProblem.html'
							},
							/*{
								id: '1-10', load: false, display: false, name: '项目基准-新', icon: 'ios-paper',
								url: linkus.location.rsc + '/02html/prj/new/projectBenchmark.html'
							},*/
							/*{
								id: '1-9', load: false, display: false, name: '项目预算-新', icon: 'ios-paper',
								url: linkus.location.rsc + '/02html/prj/prjBudget.html'
							},*/
						]
					},

					{
						id: '12', name: '人员管理', hasSubMenu: true, icon: 'icon-personnel',
						subMenuList: [
							{
								id: '12-4', load: false, display: false, name: '架构及人员', icon: 'ios-paper',
								url: linkus.location.rsc + '/02html/prj/structureAndUser.html'
							},
							/*{
								id: '12-0', load: false, display: false, name: '组织架构', icon: 'ios-people',
								url: linkus.location.rsc + '/02html/prj/prjGroupFramework.html'
							},*/
							{
								id: '12-1', load: false, display: false, name: '人员标签管理', icon: 'ios-pricetags-outline',
								url: linkus.location.rsc + '/02html/prj/prjUserTag.html'
							},
							{
								id: '12-2', load: false, display: false, name: '个人标签填报', icon: 'ios-pricetags-outline',
								url: linkus.location.rsc + '/02html/prj/personalabelReport.html?type=prj'
							},
							{
								id: '12-3', load: false, display: false, name: '干系人管理', icon: 'person-stalker',
								url: linkus.location.rsc + '/02html/prj/prjStakeholderManage.html'
							},

							{
								id: '12-5', load: false, display: false, name: '考勤数据后台管理', icon: 'person-stalker',
								url: linkus.location.rsc + '/02html/prj/prjAttendanceDataManagement.html'
							},
						]
					},
					{
						id: '2', name: '项目计划', hasSubMenu: true, icon: 'icon-xiangmujihua',
						subMenuList: [{
							id: '2-0', load: false, display: false, name: '详细计划', icon: 'ios-list',
							url: linkus.location.rsc + '/02html/prj/prjPlan.html'
						},
							{
								id: '2-1', load: false, display: false, name: '里程碑计划', icon: 'trophy',
								url: linkus.location.rsc + '/02html/prj/prjPlanMilestoneBenchmark.html'
							},
							{
								id: '2-2', load: false, display: false, name: '一级计划', icon: 'flag',
								url: linkus.location.rsc + '/02html/prj/prjLevel1Plan.html'
							},
							{
								id: '2-3', load: false, display: false, name: '二级计划', icon: 'ios-list-outline',
								url: linkus.location.rsc + '/02html/prj/prjLevel2Plan.html'
							},
							{
								id: '2-4', load: false, display: false, name: '交维计划制定', icon: 'ios-list-outline',
								url: linkus.location.rsc + '/02html/prj/prjDeliveryPlan.html'
							},
							{
								id: '2-5', load: false, display: false, name: '交维计划跟踪', icon: 'ios-list-outline',
								url: linkus.location.rsc + '/02html/prj/prjDeliveryPlanTrack.html'
							},
							{
								id: '2-6', load: false, display: false, name: '交维计划报告', icon: 'ios-list-outline',
								url: linkus.location.rsc + '/02html/prj/prjDeliveryPlanReport.html'
							}
						],
					},

					/*{
						id: '3', name: '业务管理', hasSubMenu: true, icon: 'icon-yewuguanli',
						subMenuList: [{
							id: '3-0', load: false, display: false, name: '新增业务', icon: 'document-text',
							url: ''
						},
							{
								id: '3-1', load: false, display: false, name: '待我处理的业务', icon: 'ios-list',
								url: linkus.location.query + '/querycust/prdQuery.action?qurId=5bd127e9900add501a1412e9&from=prj'
							},
							{
								id: '3-4', load: false, display: false, name: '系统&模块配置',
								url: linkus.location.rsc + '/02html/prj/prjSystemAndModule.html'
							},
							{
								id: '3-2', load: false, display: false, name: '查看所有业务', icon: 'clipboard',
								url: linkus.location.query + '/querycust/prdQuery.action?qurId=5bd125e5900add501a1412e8&from=prj'
							},
							{
								id: '3-3', load: false, display: false, name: '我的查询', icon: 'ios-box',
								url: ''
							}
						]
					},*/

					{
						id: '5', name: '项目报告', hasSubMenu: true, icon: 'icon-xiangmubaogao',
						subMenuList: [{
							id: '5-0', load: false, display: false, name: '编写项目报告', icon: 'document-text',
							url: linkus.location.rsc + '/02html/prj/prjFillReport.html'
						},
							{
								id: '5-1', load: false, display: false, name: '审批项目报告', icon: 'clipboard',
								url: linkus.location.rsc + '/02html/prj/prjApproveReport.html'
							},
							{
								id: '5-2', load: false, display: false, name: '查看项目报告', icon: 'search',
								url: linkus.location.rsc + '/02html/prj/prjCheckReport.html'
							},
							{
								id: '5-3', load: false, display: false, name: '编写小组报告', icon: 'ios-paper-outline',
								url: linkus.location.rsc + '/02html/prj/prjGroupReport.html'
							}
						]
					},

					{
						id: '6',name: '文档管理   ', hasSubMenu: true, icon: 'icon-wendangku',
						subMenuList: [{
							id: '6-0', load: false, display: false, name: '文档库', icon: 'ios-people',
							url: linkus.location.rsc + '/02html/prj/prjFile.html'
						},
							{
								id: '6-1', load: false, display: false, name: '藏经阁', icon: 'ios-people',
								url: linkus.location.rsc + "/02html/prj/prjWealthFile.html"
							}
						],
					},

					{
						id: '9', name: '统计分析', hasSubMenu: true, icon: 'icon-tongjifenxi',
						subMenuList: [
							{   id: '9-0', load: false, display: false, name: '工时统计', icon: 'ios-people',
								url: linkus.location.rsc + '/02html/prj/prjDepartWorkingHours.html'},
							{   id: '9-14', load : false, display: false, name: '成本统计', icon: 'ios-settings-strong',
								url: linkus.location.rsc + '/02html/prj/prjCostStatisticsNew.html'},
                            {   id: '9-1', load : false, display: false, name: '成本统计(旧)', icon: 'ios-settings-strong',
                                url: linkus.location.rsc + '/02html/prj/prjCostStatistics.html'},
                            {   id: '9-16', load : false, display: false, name: '交维后成本统计', icon: 'ios-settings-strong',
                                url: linkus.location.rsc + '/02html/prj/prjJwCostStatistic.html'},
							{   id: '9-2', load: false, display: false, name: '成本分析', icon: 'stats-bars',
								url: linkus.location.rsc + '/02html/prj/prjCostAnalysis.html'},
							// {   id: '9-3', load: false, display: false, name: '开发测试任务量化统计', icon: 'navicon',
//								url: linkus.location.rsc + '/02html/prj/prjDevTestStatReport.html'},
							/*{   id: '9-5', load: false, display: false, name: '经营计划对比', icon: 'ios-pulse-strong',
								url: linkus.location.rsc + '/02html/prj/prjBusinessPlanCompare.html'},*/
							{   id: '9-6', load: false, display: false, name: '系统使用分析', icon: 'ios-analytics',
								url: linkus.location.rsc + '/02html/prj/prjBusinessUsageAnalysis.html'},
							{   id: '9-15', load : false, display: false, name: '详细计划人员任务统计', icon: 'ios-settings-strong',
								url: linkus.location.rsc + '/02html/prj/prjPlanStatistics.html'},
							{   id: '9-17', load : false, display: false, name: '全项目过程完成情况', icon: 'ios-settings-strong',
								url: linkus.location.rsc + '/02html/prj/prjProcessCompletionStatistics.html'},
                            {   id: '9-18', load : false, display: false, name: '全项目成本统计', icon: 'ios-settings-strong',
                                url: linkus.location.rsc + '/02html/prj/prjActualCost.html'},
							// {
							// 	id: '9-13', load: false, display: false, name: '人员投入分析', icon: 'person-add',
							// 	url: linkus.location.rsc + '/02html/prj/prjUserManage.html'
							// },
							/*{	id:	'9-7',name:	"阿亮项目管理套表",url:"javascript:void(0)"},


							{   id: '9-12', load: false, display: false, name: '开发进度统计分析', icon: 'ios-people',queryBug: true,
								url: linkus.location.rsc + '/02html/prj/devlopmentTable.html'},


							{   id: '9-10', load: false, display: false, name: '开发人员任务统计', icon: 'ios-people',queryBug: true,
								url: linkus.location.rsc + '/02html/prj/prjDevTaskStatReport.html'},




							{   id: '9-11', load: false, display: false, name: '缺陷趋势分析', icon: 'ios-people',queryBug: true,
								url: linkus.location.rsc + '/02html/prj/prjLightDefectTrend.html'},
							{   id: '9-8', load: false, display: false, name: '测试团队"找茬"', icon: 'ios-people',queryBug: true,
								url: linkus.location.rsc + '/02html/prj/testTeamFindBug.html'},
							{   id: '9-9', load: false, display: false, name: 'BUG"天涯追杀令"',queryBug: true,
								url: linkus.location.rsc + '/02html/prj/prjBugFatwa.html'},*/

						]
					},

					{
						id: '11', name: '综合考评', hasSubMenu: true, icon: 'icon-zonghekaoping',
						subMenuList: [
							{	id: '11-7', load: false, display: false, name: '项目考评打分', icon: 'ios-settings-strong',
								url: linkus.location.rsc + '/02html/prj/prjReview.html'},
							// {   id: '11-6', load : false, display: false, name: '项目考评统计', icon: 'ios-settings-strong',
							// 	url: linkus.location.rsc + '/02html/prj/prjAssessmentStatistics.html'},
							{   id: '10-8', load: false, display: false, name: '过程管控计划', icon: 'compose',
								url: linkus.location.rsc + '/02html/prj/processControlPlan.html'},
                            {   id: '11-10', load: false, display: false, name: '项目绩效&激励', icon: 'compose',
                                url: linkus.location.rsc + '/02html/prj/new/prjInspire.html'},
							// {   id: '11-12', load: false, display: false, name: ' 绩效&激励管理', icon: 'compose',
							// 	url: linkus.location.rsc + '/02html/prj/new/prjInspireManagement.html'},
							{   id: '11-11', load: false, display: false, name: '异常偏差管理', icon: '',
								url: linkus.location.rsc + '/02html/prj/prjBugDeviationMgt.html'},

						]
					},

					{
						id: '7', name: '系统配置   ', hasSubMenu: true, icon: 'icon-xitongpeizhi',
						subMenuList: []
					},
					{
						id: '13', name: '销售过程管理   ', hasSubMenu: false, icon: '',
						url: ('devlinkus.asiainfo.com' === window.location.host || 'testlinkus.asiainfo.com' === window.location.host
							? linkus.location.rsc + '/pms-sales/#/customer/visit-management'
							: 'http://aispm.asiainfo.com/')
					},
				],
				myQurs: [],
				createBizModal: false,
				createBizFrameSrc: '',
				createdBizViewCode: '',
				bizId: '',
				showDrawer: false,
				tasksTipShow: false,
				todoTasksNum: 0,
				todoTasksKey: '',
				todoTasks: [],
				todoBaseTasks: [],
				myTodoApply: [],
				todoRate: [],
				taskLodingOut:false,
				rateLoadingOut:false,
				visible: false,
				isSysAdmin : false,
				isPrjOrSysOrMgrOrBuMgr:false,//是否是项目经理、管理员、BU项目预算管理员
				prjInfoModal: false,
				loadUserByFuzzyTime : null,
				loadUserByFuzzyAjax : null,
				//基本信息
				prjInfo: {
					prjId: null,
					// 项目编码
					prjCode: null,
					// 项目名称
					prjName: null,
					// 项目经理
					pmUser: {
						userId: null,
						userName: null,
						loginName: null,
						jobCode: null
					},
					isOmPrj: false,
				},
				// 项目管理员
				prjAdmin: {
					userId: null,
					userName: null,
					loginName: null,
					jobCode: null
				},

				prjInfoValidate: {
					prjCode: [{
						required: true,
						validator: validatePrjCode
					}],
					prjName: [{
						required: true,
						type: 'string',
						message: '请填写项目名称'
					}],
					pmUser: [{
						required: true,
						validator: validatePm
					}]
				},
				//是否bu预算管理员
				isBuBudget:false,
				isCmcBuBudget: false,


			gradedApplyCount:0,
			gradedUnGradeCount:0,
			aiBuCodeName:'aiBu',
			abpAdminRoleId:'62a69c9c66a6538180672f94',
			isAbpBuAdmin:false,
			abpProvCodeName:'abpProv',
			buRoleId: '5c501a88900add501a1418c2',
			isProvOperateAdmin:false,
			isBuOmAdmin: false,
			prjQualityReview: "11-16",

			buAndQaAccess: false,

			pmsValueItemStatusBeforeSaleId: "67779b06bc0f0b3441830bd7",// "待价值交付确认"状态id
			pmsValueItemStatusModifyId: "67779b06bc0f0b3441830bd8",// "待修改"状态id
			pmsValueItemStatusSelfAssessmentId: "67eb486dfee7ca335e25d482", //"PM自评"状态id
			pmsValueItemStatusSelfBeforeSaleAssessId: "67eb4875345a6b2a5ca17294", //"价值交付评估"状态id
        };
    },
    methods: {

		//查询价值交付项待办数
		queryValueItemCount: function() {
			var sf = this;
			sf.valueItemUnConfirmedCount = 0;
			sf.valueItemUnModifyCount = 0;
			sf.valueItemPmSelfAssessCount = 0;
			sf.valueItemAssessCount = 0;
			sf.countCornermarkByTypeId(sf.pmsValueItemStatusBeforeSaleId);
			sf.countCornermarkByTypeId(sf.pmsValueItemStatusModifyId);
			sf.countCornermarkByTypeId(sf.pmsValueItemStatusSelfAssessmentId);
			sf.countCornermarkByTypeId(sf.pmsValueItemStatusSelfBeforeSaleAssessId);
		},

        //根据价值项交付状态查询价值交付项待办数
		countCornermarkByTypeId: function(typeId) {
			var sf = this;
			$.ajax({
				url: linkus.location.prj + '/preSalesCtrl/countCornermark',
				type: "post",
				data: {
					typeId: typeId,
				},
				success: function (data) {
					if (!!data && !!data.success && !!data.data && JSON.stringify(data.data) != "{}") {
						var count = 0;
						for (var key in data.data) {
							count += (data.data[key] || 0);
						}
						if(typeId == sf.pmsValueItemStatusBeforeSaleId) {
							sf.valueItemUnConfirmedCount = count;
						}else if(typeId == sf.pmsValueItemStatusModifyId) {
							sf.valueItemUnModifyCount = count;
						}else if(typeId == sf.pmsValueItemStatusSelfAssessmentId) {
							sf.valueItemPmSelfAssessCount = count;
						}else if(typeId == sf.pmsValueItemStatusSelfBeforeSaleAssessId) {
							sf.valueItemAssessCount = count;
						}
					}
				},
				error: function (data) {
				}
			});
		},

		//跳转到价值交付页面
		goToValueItem: function () {
			var sf = this;
			window.open('/02html/prj/valueDelivery.html');
		},

		//选择类型
		selectType: function() {
			var sf = this;
			if(!sf.typeValue || sf.typeValue != "prjBmk") {
				sf.levelValues = [];
			}
			sf.loadTodoTasks();
		},

		//选择分类
		selectPrjLevel: function() {
			var sf = this;
			sf.loadTodoTasks();
		},

		//查询项目分类列表
		getPrjLevels: function () {
			var sf = this;
			sf.levelList = [];
			$.ajax({
				url: linkus.location.prj + "/prjInfoCtrl/queryPrjLevelByLoginUser.action",
				dataType: "json",
				success: function (levels) {
					sf.levelList = levels || [];
				}
			});
		},

		getScreenToken:function(){
    		var sf = this;
			$.ajax({
				url: linkus.location.prj + '/prjCostStatisticsCtrl/getToken.action',
				type: 'get',
				dataType:'text',
				success: function (data) {
					for(var i = 0; i < sf.menuList.length; i++) {
						if(sf.menuList[i].id === '0-0') {
							sf.menuList[i].subMenuList.forEach(function (item) {
								if(item.id === '0-2'){
									item.url =  item.url+'?ak=v5XmgsqT&access_token='+data;
									console.log(item.url);
								}
							})
						}
					}
				},
				error: function (text) {

				}
			});

		},

    	resetFields: function () {
			var sf = this;
			sf.$refs['prjInfoForm'].resetFields();
			Vue.set(sf.prjInfo, 'pmUser', {
				userId: null,
				userName: null,
				loginName: null,
				jobCode: null
			});
			sf.prjAdmin['userId'] = null;
			sf.prjAdmin['userName'] = null;
			sf.prjAdmin['loginName'] = null;
			sf.prjAdmin['jobCode'] = null;
			sf.prjInfo.prjCode = null;
			sf.prjInfo.prjName = null;
		},
		prjModalOk: function () {
			var sf = this;
			sf.addPrjInfo();
		},
		addPrjInfo: function () {
			var sf = this;
			var teSysUserVo = {
     				  id        : sf.prjAdmin.userId,
     				  loginName : sf.prjAdmin.loginName,
     				  userName  : sf.prjAdmin.userName,
     				  jobCode   : sf.prjAdmin.jobCode,
     		  };
			sf.prjInfo["prjAdmin"] = {};
			sf.prjInfo["prjAdmin"] = teSysUserVo;
			sf.$refs['prjInfoForm'].validate(function(valid){
				if (valid) {
					$.ajax({
						url: linkus.location.prj + '/prjInfoCtrl/addPrjInfo.action',
						headers: {
							'Content-Type': 'application/json;charset=utf-8'
						},
						data: JSON.stringify(sf.prjInfo),
						type: 'post',
						success: function (data) {
							sf.$Message.success("添加项目成功");
							sf.resetFields();
							sf.prjInfoModal = false;
						},
						error: function (text) {
							sf.$Message.error(eval('(' + text.responseText + ')')
								.errorMessage);
						}
					});
				} else {
					window.console && console.log("error");
				}

			});
		},

     prjModalCancel: function () {
			 var sf = this;
			 sf.resetFields();
		     sf.prjInfoModal = false;
	  },
	//查询登陆用户是否是项目经理、管理员、BU项目预算管理员
	queryLoginUserPrivalage:function(){
		var sf = this;
		$.ajax({
			url : linkus.location.prj +'/prjCalendar/checkLoginUser.action',
			type : 'post',
			headers : {'Content-Type' : 'application/json;charset=utf-8'},
			dataType : 'JSON',
			success:function(data){
				//项目日历：项目经理、管理员、BU项目预算管理员可见
				if(data){
					sf.isPrjOrSysOrMgrOrBuMgr = true;
					for(var i = 0; i < sf.menuList.length; i++) {
						if(sf.menuList[i].id == '7') {
							sf.menuList[i].subMenuList.push(
								{
									id: '7-4', load: false, display: false, name: '项目日历', icon: 'ios-browsers-outline',
									url: linkus.location.rsc + '/02html/prj/prjCalendar.html'
								}
							)
							sf.menuList[i].subMenuList.push(
								{
								     id: '7-5', load: false, display: false, name: '系统&模块配置',
								     url: linkus.location.rsc + '/02html/prj/prjSystemAndModule.html'
							    }
							)
						}
					}
				}
				// else if(!sf.isSysAdmin){
				// 	for(var i = 0; i < sf.menuList.length; i++) {
				// 		if(sf.menuList[i].id == '7') {
				// 			sf.menuList.splice(i,1);
				// 		}
				// 	}
				// }
			}
		})
	},
	  //查询登录用户是不是系统管理员
      querySysAdmin : function() {
     		  var sf = this;
     		  var teSysUserVo = {
     				  id        : sf.currentUser.id,
     				  loginName : sf.currentUser.loginName,
     				  userName  : sf.currentUser.userName,
     				  jobCode   : sf.currentUser.jobCode,
     		  };
     		  $.ajax({
 	   			url : linkus.location.prjuser +'/sysDefRoleUserCtrl/querySysAdmin.action',
 	   			type : 'post',
 	 	   		headers : {'Content-Type' : 'application/json;charset=utf-8'},
 	   			data : JSON.stringify(teSysUserVo),
 	   			async: false,
 	   			dataType : 'JSON',
 	   			success : function(data) {
 	   				if(data.length>0) {
						sf.isSysAdmin = true;

						for (var i = 0; i < sf.menuList.length; i++) {
							if (sf.menuList[i].id == '7') {
								// 配置计划模板、新增项目：系统管理员可见
								sf.menuList[i].subMenuList.push({
									id: '7-0', load: false, display: false, name: '配置计划模板', icon: 'clipboard',
									url: linkus.location.rsc + '/02html/prj/prjPlanTemplateConfig.html'
								});
								sf.menuList[i].subMenuList.push({
									id: '7-1', load: false, display: false, name: '新增项目', icon: 'ios-list-outline',
									url: ''
								});

							}
						}

					}
					sf.queryLoginUserPrivalage();
 	   			},
 	   			error : function(data) {
 	   			       window.console && console.log(data);
 	   			}
 	  		 });
     	},
		//是否bu预算管理员
		queryBudgetMan:function(){
    		var sf = this;
			$.ajax({
				url: linkus.location.prj+'/prjCapabilityAssessCtrl/hasAuth.action',
				type:'get',
				success: function(data){
					if(data && data.length > 0){
						sf.isBuBudget = true;
						sf.$emit('get_bu_admin',sf.isBuBudget)
						if(data.indexOf("185") != -1 || data.indexOf("183") != -1){
							sf.isCmcBuBudget = true
						}
					}else{
						sf.isBuBudget = false;
						sf.isCmcBuBudget = false;
					}
					if(sf.isBuBudget){
						for(var i = 0; i < sf.menuList.length; i++) {
							if(sf.menuList[i].id == '11') {
								if(sf.isCmcBuBudget){
									sf.menuList[i].subMenuList.splice(0,0,{id: '9-7',name:	"PM能力评估分类",url:"javascript:void(0)"});
									sf.menuList[i].subMenuList.splice(1,0,{id: '11-2', load: false, display: false, name: '发起评分', icon: '',pmAbility:true, url: '/02html/prj/PmInitiateRating.html'})
									sf.menuList[i].subMenuList.splice(2,0,{id: '11-5', load: false, display: false, name: '评分明细', icon: '',pmAbility:true, url: '/02html/prj/pmAbilityAssessmentSearch.html'})
									sf.menuList[i].subMenuList.splice(3,0,{id: '11-3', load: false, display: false, name: '评分统计', icon: '',pmAbility:true, url: '/02html/prj/pmAbilityAssessmentStatistics.html'})
									sf.menuList[i].subMenuList.splice(4,0,{id: '11-4', load: false, display: false, name: '能力项统计', icon: '',pmAbility:true, url: '/02html/prj/pmAbilityAssessmentScoreStatistics.html'})
								}
								//sf.menuList[i].subMenuList.splice(6,0,{id: '11-6', load: false, display: false, name: '项目考评统计', icon: 'ios-settings-strong',url: '/02html/prj/prjAssessmentStatistics.html'})
							}else if(sf.menuList[i].id == '12'){
								sf.menuList[i].subMenuList.splice(4,0,{id: '12-6', load: false, display: false, name: '干系人库', icon: 'person-stalker', url: linkus.location.rsc + '/02html/prj/stakeholderLibrary.html'})

							}else if(sf.menuList[i].id == '7'){
								sf.menuList[i].subMenuList.push(
									{
										id: '7-6', load: false, display: false, name: '项目经理库',
										url: linkus.location.rsc + '/02html/prj/projectManagerLibrary.html'
									}
								)
								sf.menuList[i].subMenuList.push({
									id: '7-8', load: false, display: false, name: '系统角色配置', icon: 'ios-list-outline',
									url: linkus.location.rsc + '/02html/prj/new/roleConfiguration.html'
								});
								sf.menuList[i].subMenuList.splice(6,0,{id: '10-7', load: false, display: false, name: '过程管控模板', icon: '', url: '/02html/prj/projectProcessControlTemplate.html'})
							}else if(sf.menuList[i].id == '0-0' && sf.isCmcBuBudget){
								sf.menuList[i].hasSubMenu = true;
							}
						}
					}else {
						for(var i = 0; i < sf.menuList.length; i++) {
							if(sf.menuList[i].id == '11') {
								sf.menuList[i].subMenuList.splice(0,0,{id: '9-7',name:	"PM能力评估分类",url:"javascript:void(0)"});
								sf.menuList[i].subMenuList.splice(1,0,{id: '11-5', load: false, display: false, name: '评分明细', icon: '',pmAbility:true, url: '/02html/prj/pmAbilityAssessmentSearch.html'});
							}
						}
					}
					sf.checkBuHasUseAbp();
					sf.queryBuOrProvOperateAdmin();
				},
				error	 	: 	function(text){
					sf.$Message.error('查询是否BU预算管理员失败，请联系管理员!');
				}
			});
		},

		//是否abp bu级管理员
		queryAbpBuAdmin:function(){
			var sf = this;
			$.ajax({
				url: linkus.location.prjuser+'/sysDefRoleUserCtrl/queryRoleUsers.action',
				data: {
					defTypeCodeName: sf.aiBuCodeName,
					roleId: sf.abpAdminRoleId,
				},
				type:'get',
				dataType : 'JSON',
				success: function(res){
					if(res && res.status == "success" && res.data && res.data.length > 0){
						sf.isAbpBuAdmin = true;
					}else{
						sf.isAbpBuAdmin = false;
					}
				},
				error	 	: 	function(text){
					sf.$Message.error('查询是否Abp BU级管理员失败，请联系管理员!');
				}
			});
		},
		//查询是否是BU或者省份运营管理员
		queryBuOrProvOperateAdmin: function() {
			var sf = this;
			$.ajax({
				url: linkus.location.prjuser + '/sysDefRoleUserCtrl/queryRoleUsers.action',
				data: {
					roleId: sf.buRoleId,
				},
				type:'get',
				dataType : 'JSON',
				success: function(res){
					if(res && res.status == "success" && res.data && res.data.length > 0) {
						for(var i = 0; i < sf.menuList.length; i++) {
							if(sf.menuList[i].id == '9') {
								sf.menuList[i].subMenuList.push({
									id: '9-19', load: false, display: false, name: '省同类超基线统计',
									url: linkus.location.rsc + '/02html/prj/provSimilarHyperbaselineStatistics.html'
								});
							}
							if(sf.menuList[i].id == '11') {
								if (sf.isBuBudget) {
									sf.menuList[i].subMenuList.splice(7,0,{
										id: '11-6', load: false, display: false, name: '项目考评统计', icon: 'ios-settings-strong',
										url: '/02html/prj/prjAssessmentStatistics.html',
									});
								}else {
									sf.menuList[i].subMenuList.splice(4,0,{
										id: '11-6', load: false, display: false, name: '项目考评统计', icon: 'ios-settings-strong',
										url: '/02html/prj/prjAssessmentStatistics.html',
									});
								}

								sf.menuList[i].subMenuList.splice(12, 0, {
									id: '11-13', load: false, display: false, name: '大项目跟踪管理',
									url: linkus.location.rsc + '/02html/prj/largeProjectTrackingMgt.html'
								});
							}

						}
					}else {
						if(!!sf.loginUserSbuId  && sf.loginUserSbuId  == "185") {
							for(var i = 0; i < sf.menuList.length; i++) {
								if(sf.menuList[i].id == '11') {
									sf.menuList[i].subMenuList.splice(12, 0, {
										id: '11-13', load: false, display: false, name: '大项目跟踪管理',
										url: linkus.location.rsc + '/02html/prj/largeProjectTrackingMgt.html'
									});
								}
							}
						}
					}
					sf.checkBuOmAdmin();
				},
				error: function(data) {
					sf.checkBuOmAdmin();
				}
			});
		},

		//检验是否是BU运营管理员
		checkBuOmAdmin:function() {
			var sf = this;
			sf.isBuOmAdmin = false;
			$.ajax({
				url: linkus.location.prj + "/prjCapabilityAssessCtrl/hasAuth.action",
				success: function (data) {
					if(!!data && data.length > 0) {
						sf.isBuOmAdmin = true;
						sf.$parent.isBuOmAdmin = true;
						for(var i = 0; i < sf.menuList.length; i++) {
							if(sf.menuList[i].id == '9') {
								sf.menuList[i].subMenuList.push({
									id: '9-20', load: false, display: false, name: '交通费统计',
									url: linkus.location.rsc + '/02html/prj/travelFeeStatistics.html'
								});
							}
							if(sf.menuList[i].id == '11') {
								if(!!data.includes("185")) {
									// sf.menuList[i].subMenuList.splice(13, 0, {
									// 	id: '11-14', load: false, display: false, name: '项目经理排名',
									// 	url: linkus.location.rsc + '/02html/prj/pmUserRanking.html'
									// });
									sf.menuList[i].subMenuList.splice(14, 0, {
										id: '11-15', load: false, display: false, name: '体系成绩预测',
										url: linkus.location.rsc + '/02html/prj/prjPredictPerformance.html'
									});
									sf.menuList[i].subMenuList.splice(15, 0, {
										id: '11-20', load: false, display: false, name: '价值交付',
										url: linkus.location.rsc + '/02html/prj/valueDelivery.html'
									});
								}
								// sf.menuList[i].subMenuList.splice(16,0,{id: '11-16',name:	"项目质量考评",url:"javascript:void(0)"});
								// sf.menuList[i].subMenuList.splice(17,0,{   id: '11-17', load: false, display: false, name: '项目质量考评打分', icon: '',  prjQualityReview: true, url: linkus.location.rsc + '/02html/prj/prjQualityReview.html'});
								// sf.menuList[i].subMenuList.splice(18,0,{   id: '11-18', load: false, display: false, name: 'SRD质量考评打分', icon: '',  prjQualityReview: true, url: linkus.location.rsc + '/02html/prj/srdQualityReview.html'});
							}
						}
					}else {
						if(!!sf.loginUserSbuId  && sf.loginUserSbuId  == "185") {
							for(var i = 0; i < sf.menuList.length; i++) {
								if(sf.menuList[i].id == '11') {
									// sf.menuList[i].subMenuList.splice(13, 0, {
									// 	id: '11-14', load: false, display: false, name: '项目经理排名',
									// 	url: linkus.location.rsc + '/02html/prj/pmUserRanking.html'
									// });
									sf.menuList[i].subMenuList.splice(14, 0, {
										id: '11-15', load: false, display: false, name: '体系成绩预测',
										url: linkus.location.rsc + '/02html/prj/prjPredictPerformance.html'
									});
									sf.menuList[i].subMenuList.splice(15, 0, {
										id: '11-20', load: false, display: false, name: '价值交付',
										url: linkus.location.rsc + '/02html/prj/valueDelivery.html'
									});
								}
							}
						}
						// for(var i = 0; i < sf.menuList.length; i++) {
						// 	if(sf.menuList[i].id == '11') {
						// 		sf.menuList[i].subMenuList.splice(16,0,{id: '11-16',name:	"项目质量考评",url:"javascript:void(0)"});
						// 		sf.menuList[i].subMenuList.splice(17,0,{   id: '11-17', load: false, display: false, name: '项目质量考评打分', icon: '',  prjQualityReview: true, url: linkus.location.rsc + '/02html/prj/prjQualityReview.html'});
						// 		sf.menuList[i].subMenuList.splice(18,0,{   id: '11-18', load: false, display: false, name: 'SRD质量考评打分', icon: '',  prjQualityReview: true, url: linkus.location.rsc + '/02html/prj/srdQualityReview.html'});
						// 	}
						// }
					}
				},
				error: function(data) {
					// for(var i = 0; i < sf.menuList.length; i++) {
					// 	if(sf.menuList[i].id == '11') {
					// 		sf.menuList[i].subMenuList.splice(15,0,{id: '11-16',name:	"项目质量考评",url:"javascript:void(0)"});
					// 		sf.menuList[i].subMenuList.splice(16,0,{   id: '11-17', load: false, display: false, name: '项目质量考评打分', icon: '',  prjQualityReview: true, url: linkus.location.rsc + '/02html/prj/prjQualityReview.html'});
					// 		sf.menuList[i].subMenuList.splice(17,0,{   id: '11-18', load: false, display: false, name: 'SRD质量考评打分', icon: '',  prjQualityReview: true, url: linkus.location.rsc + '/02html/prj/srdQualityReview.html'});
					// 	}
					// }
					sf.$Message.error('校验BU运营管理员错误，请联系管理员',3);
				}
			});
		},

		//查询省份运营管理员
		queryProvOperateAdmin:function(){
			var sf = this;
			$.ajax({
				url: linkus.location.prjuser+'/sysDefRoleUserCtrl/queryRoleUsers.action',
				data: {
					defTypeCodeName: sf.abpProvCodeName,
					roleId: sf.buRoleId,
				},
				type:'get',
				dataType : 'JSON',
				success: function(res){
					if(res && res.status == "success" && res.data && res.data.length > 0){
						sf.isProvOperateAdmin = true;
					}else{
						sf.isProvOperateAdmin = false;
					}
				},
				error	 	: 	function(text){
					sf.$Message.error('查询省份运营管理员失败，请联系管理员!');
				}
			});
		},

		//校验bu是否使用了abp
		checkBuHasUseAbp: function() {
			var sf = this;
			$.ajax({
				url: linkus.location.prjuser + '/sysDefCtrl/checkBuHasUseAbp.action',
				data: {
					sbuId: sf.loginUserSbuId,
				},
				type: 'post',
				dataType: 'JSON',
			}).done(function (data) {
				var isUseAbp = false;
				if (!!data && JSON.stringify(data) != "{}" && !!data.cndtItems && data.cndtItems.length > 0) {
					for (var i = 0; i < data.cndtItems.length; i++) {
						var item = data.cndtItems[i];
						if (item.cid == sf.userAbpId) {
							isUseAbp = true;
							break;
						}
					}
				}
				for(var i = 0; i < sf.menuList.length; i++) {
					if (sf.menuList[i].id == '1'){
						if (!!isUseAbp) {
							sf.menuList[i].subMenuList.splice(1, 0, {
								id: '1-10', load: false, display: false, name: '项目基准-新', icon: 'ios-paper',
								url: linkus.location.rsc + '/02html/prj/new/projectBenchmark.html'
							});
							sf.menuList[i].subMenuList.splice(2, 0, {
								id: '1-9', load: false, display: false, name: '项目预算-新', icon: 'ios-paper',
								url: linkus.location.rsc + '/02html/prj/prjBudget.html'
							});
						} else {
							sf.menuList[i].subMenuList.splice(1, 0, {
								id: '1-2', load: false, display: false, name: '项目基准', icon: 'ios-paper',
								url: linkus.location.rsc + '/02html/prj/prjBasicInfo.html'
							});
							sf.menuList[i].subMenuList.splice(2, 0, {
								id: '1-8', load: false, display: false, name: '项目预算', icon: 'ios-calculator',
								url: linkus.location.rsc + '/02html/prj/prjBenchmarkBudgetRpt.html'
							});
						}
                        if((!!isUseAbp && !!sf.isBuBudget) || !!sf.isAbpBuAdmin || !!sf.isProvOperateAdmin) {
                        	sf.menuList[i].subMenuList.push({
								id: '1-11', load: false, display: false, name: '预算项目信息管理', icon: 'alert-circled',
								url: linkus.location.rsc + '/02html/prj/budgetPrjInfoManagement.html'
							});
						}
                        if(!!isUseAbp) {
                        	sf.menuList[i].subMenuList.push({
 							   id: '1-12', load: false, display: false, name: '项目分类管理', icon: 'ios-paper',
 							   url: linkus.location.rsc + '/02html/prj/new/projectGradingManagement.html'
 							});
							sf.menuList[i].subMenuList.push({
								id: '1-13', load: false, display: false, name: '项目需求变更', icon: 'ios-paper',
								url: linkus.location.rsc + '/02html/prj/prjRequirementChange.html'
							});
                        }
					}

					// if (sf.menuList[i].id == '11') {
					// 	if(!!isUseAbp) {
					// 		sf.menuList[i].subMenuList.splice(8, 0, {
					// 			id: '11-12', load: false, display: false, name: 'EF考评统计', icon: '',
					// 			url: linkus.location.rsc + '/02html/prj/EFPrjView.html'
					// 		});
					// 	}
					// }

				}
			});
		},

		//查询是否bu领导
		queryBuLeadership:function(){
			var sf = this;
			$.ajax({
				url: linkus.location.prj+'/prjCapabilityAssessCtrl/hasPmLeaderAuth.action',
				type:'get',
				success: function(data){
					if(data){
						for(var i = 0; i < sf.menuList.length; i++) {
							if(sf.menuList[i].id == '11') {
								sf.menuList[i].subMenuList.splice(0,0,{id: '9-7',name:	"PM能力评估分类",url:"javascript:void(0)"});
								sf.menuList[i].subMenuList.splice(1,0,{   id: '11-9', load: false, display: false, name: '领导工作台', icon: '',pmAbility:true, url: linkus.location.rsc + '/02html/prj/PMLeadershipWorkbench.html'})
								sf.menuList[i].subMenuList.splice(2,0,{id: '11-3', load: false, display: false, name: '评分统计', icon: '',pmAbility:true, url: '/02html/prj/pmAbilityAssessmentStatistics.html'})
							}
						}
					}else{
						for(var i = 0; i < sf.menuList.length; i++) {
							if(sf.menuList[i].id == '11') {
								var ifHas = false;
								for (var j=0;j<sf.menuList[i].subMenuList.length;j++){
									if (sf.menuList[i].subMenuList[j].id == '9-7'){
										ifHas = true;
										break;
									}
								}
								if (!ifHas){
									sf.menuList[i].subMenuList.splice(0,0,{id: '9-7',name:	"PM能力评估分类",url:"javascript:void(0)"});
								}
								sf.menuList[i].subMenuList.splice(1,0,{   id: '11-1', load: false, display: false, name: '我的打分', icon: '',pmAbility:true, url: linkus.location.rsc + '/02html/prj/PMCompetencyAssessmentMyGrade.html'})
							}
						}

					}
				},
				error	 	: 	function(text){
					sf.$Message.error('查询是否BU项目领导失败，请联系管理员!');
				}
			});
		},

		showCreateBizCode: function(t) {
			var sf = this;
			sf.$Message.success({
				duration: 0,
				closable :true,
				render: function(h){
					return h('span', [
						h('a', {
							style:{
								width:'3px'
							},
							on: {
								click: function() {
									sf.showBizView();
									sf.$Message.destroy();
								},
							}
						},t),
					])
				}
			});
		},
		logout: function () {
			window.location.href = linkus.location.passport + "/logout";
		},
		changePage: function (pageId, urlParams, forceReload) {
			var sf = this;
			if (pageId == "3-0") {
				sf.showCreateBizModal();
				return;
			}
			if (pageId == "9-7") {
				return;
			}
			if (pageId == "7-1"){
				sf.prjInfoModal = true;
				return;
			}
			for (var i = 0; i < sf.pageList.length; i++) {
				var item = sf.pageList[i];
				if (item.id == pageId) {
				    var url = item.url;
					window.open(url);
					break;
				}
			}
		},
		loadCurrentUser: function () {
			var sf = this;
			sf.loginUserSbuId = "";
			return $.ajax({
				url: linkus.location.prjuser + '/sysUserCtrl/queryByLoginName.action',
				type: 'post',
				dataType: 'JSON',
				async: false,
			}).done(function (teSysUser) {
				sf.currentUser = teSysUser;
				sf.$parent.currentUser = teSysUser;
				if(!!sf.currentUser && JSON.stringify(sf.currentUser) != "{}") {
					sf.loginUserSbuId = sf.currentUser.sbuId;
				}
				sf.queryBudgetMan();
				sf.queryAbpBuAdmin();

				sf.queryProvOperateAdmin();
				sf.queryBuAndQaAccess();
			});
		},
		updateJWTToken: function () {
			var sf = this;
			return $.ajax({
				url: linkus.location.passport + '/update',
				type: 'post',
			}).done(function () {
			});
		},
		getMyQurs: function (qurId) {
			var sf = this;
			$.ajax({
				url: linkus.location.query + "/querycust/getUserNoCndtedQueries.action",
				data: {},
				async: true,
				success: function (myQurs) {
					sf.myQurs = myQurs || [];

					if (sf.myQurs.length > 0) {
						/*var bizManage = sf.menuList[4];
						bizManage.subMenuList = [{
							id: '3-0', load: false, display: false, name: '新增业务', icon: 'document-text',
							url: ''
						},
						{
							id: '3-1', load: false, display: false, name: '待我处理的业务', icon: 'ios-list',
							url: linkus.location.query + '/querycust/prdQuery.action?qurId=5bd127e9900add501a1412e9&from=prj'
						},
							{
								id: '3-4', load: false, display: false, name: '系统&模块配置',
								url: linkus.location.rsc + '/02html/prj/prjSystemAndModule.html'
							},
						{
							id: '3-2', load: false, display: false, name: '查看所有业务', icon: 'clipboard',
							url: linkus.location.query + '/querycust/prdQuery.action?qurId=5bd125e5900add501a1412e8&from=prj'
						},
						{
							id: '3-3', load: false, display: false, name: '我的查询', icon: 'ios-box',
							url: ''
						}
						];*/

						for (var i = 0; i < sf.myQurs.length; i++) {
							var myQuerysMenu = {};
							myQuerysMenu['display'] = false;
							myQuerysMenu['id'] = '3-' + (i + 6);
							myQuerysMenu['load'] = false;
							myQuerysMenu['name'] = sf.myQurs[i].qurName;
							myQuerysMenu['url'] = linkus.location.query + '/querycust/prdQuery.action?qurId=' + sf.myQurs[i].qurId + '&from=prj';
							myQuerysMenu['queryMenu'] = true;
							//bizManage.subMenuList.splice(4, 0, myQuerysMenu);

							if (!!qurId && qurId == sf.myQurs[i].qurId) {
								sf.activeMenuId = '3-' + (i + 6);

								sf.$nextTick(function () {
									//加载菜单
									sf.changePage(sf.activeMenuId);
								});
							}
						}
					}
				}
			});
		},
		showCreateBizModal: function () {
			var sf = this;
			var iframeId = Math.random() * 1000000;
			sf.createBizFrameSrc = linkus.location.biz + "/forward.action?t=prd/popWin/createBiz&noflow=false&from=prj&iframeId=" + iframeId;
			sf.createBizModal = true;
		},
		cancelCreateBizModal: function () {
			var sf = this;
			sf.createBizFrameSrc = '';
			sf.createdBizViewCode = '';
			sf.bizId = '';
			sf.createBizModal = false;
		},
		showBizView: function () {
			var sf = this;
			window.open(linkus.location.biz + '/forward.action?t=biz/prdBizView&bizId=' + sf.bizId + "&from=prj");
		},
		addToBizViewEventListener: function () {
			var sf = this;
			window.addEventListener("message", function (e) {
				if (e.data && e.data.info) {
					sf.bizId = e.data.info.bizId;
					sf.showCreateBizCode(e.data.info.bizCode + " 已创建！");
				} else if(e.data && e.data.ifrmHeight) {
					$("#crtBizIfrm").css("height",( e.data.ifrmHeight + 165) + "px")
				}else if (e.data && e.data.qurId) {
					sf.getMyQurs(e.data.qurId);
				}
			});
		},

		showTodoTasks: function() {
			this.showDrawer = true;
			$('body').css('overflow','hidden');
		},

		hideTodoTasks: function() {
			this.showDrawer = false;
			this.todoTasksKey = '';
			$('body').css('overflow','auto');
		},

		loadTodoTasks: function(){
			var sf = this;
			sf.taskLodingOut = false;
			sf.spinShow = true;
			return $.ajax({
				url: linkus.location.prj + '/prjSflTask/getMyTodoTask.action',
				type: 'post',
				dataType: 'JSON',
				data: {
					type: (!!sf.typeValue && sf.typeValue != "{}") ? sf.typeValue : null,
					levelIds: sf.levelValues,
				}
			}).done(function (data) {
				if(!!data && JSON.stringify(data) != "{}") {
					sf.todoBaseTasks = data || [];
				}else {
					sf.todoBaseTasks = [];
				}
				sf.$parent.todoTasks = data || [];
				if(sf.currentUser.loginName === 'cangjy'){
					sf.loadTodoTasksPending();
				}else{
					sf.filterTodoTasksByKey();
					if(!sf.todoTasks.length){
						sf.tasksTipShow = true;
					}
					sf.taskLodingOut =true;
					if(sf.typeValue != 'prjBmk'){
						sf.loadMyTodoApply();
					}
				}
			});
		},


		loadMyTodoApply: function() {
			var sf = this;
			sf.taskLodingOut =true;
			$.ajax({
				url: linkus.location.prjuser + '/sysUserApplyCtrl/getMyTodoApply.action',
				type: 'post',
				dataType: 'JSON'
			}).done(function (res) {
				if(res.success){
					sf.myTodoApply = res.data || [];
					let todoTasks = sf.myTodoApply.map(item => {
						return {
							prjId: item.srcDef.cid,
							name:item.rcdStatus.name + '-' + item.srcDef.codeName + '/' + item.srcDef.name,
							node: {
								name:'项目激励申请',
							},
							codeName:item.type.codeName,
							submitUserName:item.applyUser.userName,
							submitTime:item.applyTime,
						}
					});
					sf.myTodoApplyNum = sf.myTodoApply.length;
					sf.todoBaseTasks = sf.todoBaseTasks.concat(todoTasks);
					sf.taskLodingOut =false;
					if(!sf.todoTasks.length){
						sf.tasksTipShow = true;
					}
					sf.filterTodoTasksByKey();
				}else{
					sf.$Message.error('获取待审批数据失败，请联系管理员',3);
				}
			});
		},

		loadTodoTasksPending:function(){
			var sf = this;
			$.ajax({
				url: linkus.location.prj+'/prjCapabilityAssessCtrl/getAwaitCheckInfo.action',
				success: function (data) {
					if(!!data && JSON.stringify(data) != "{}") {
						data =  data || [];
					}else {
						data = [];
					}
					for(var i = 0; i < data.length;i++) {
						data[i].isPending = true;
						sf.todoBaseTasks.push(data[i]);
					}
					sf.filterTodoTasksByKey();
					if(!sf.todoTasks.length){
						sf.tasksTipShow = true;
					}
					sf.taskLodingOut =true;
					if(sf.typeValue != 'prjBmk'){
						sf.loadMyTodoApply();
					}
				},
				error : function(data) {
					sf.filterTodoTasksByKey();
					sf.tableLoading = false;

					sf.$Message.error('获取待审批数据失败，请联系管理员',3);
				}
			});
		},


		//待我评分
		loadTodoRate: function(){
			var sf = this;
			sf.rateLoadingOut =false;
			return $.ajax({
				url: linkus.location.prj + '/prjCapabilityAssessCtrl/getAwaitAppraiseInfo.action',
				type: 'post',
				dataType: 'JSON'
			}).done(function (data) {
				sf.todoRate = data || [];
				sf.$parent.todoRate = data || [];
				sf.rateLoadingOut =true;
			});
		},

		//新增项目点击记录
		addPrjClickLog: function(prjId){
			var sf = this;
			var data = {
				defId: prjId
			};

			return $.ajax({
				url : linkus.location.prjuser + '/sysDefClickLogCtrl/addClickLog.action',
				headers : {'Content-Type' : 'application/json;charset=utf-8'},
				type : 'post',
				data : JSON.stringify(data),
				dataType : 'JSON'
			});
		},

		gotoApprove: function(task){
			var sf = this;

			sf.hideTodoTasks();
			this.addPrjClickLog(task.prjId);

			if(task.maintPlanReport){
				window.open(linkus.location.rsc + '/02html/prj/prjDeliveryPlanReport.html?prjId=' + task.prjId + '&taskId=' + task.id);
			}else if(task.maintPlan){
				window.open(linkus.location.rsc + '/02html/prj/prjDeliveryPlan.html?prjId=' + task.prjId + '&taskId=' + task.id);
			}else if(task.transfer){
				window.open(linkus.location.rsc + '/02html/prj/prjDeliveryImplementManagement.html?prjId=' + task.prjId + '&taskId=' + task.id);
			}else if(task.newTask){
				window.open(linkus.location.rsc + '/02html/prj/new/projectBenchmark.html?prjId=' + task.prjId + '&taskId=' + task.id);
			}else if(task.prjChange){
				window.open(linkus.location.rsc + '/02html/prj/prjRequirementChangeDetails.html?prjId=&subPrjId=' + task.prjId + '&bizId=' + task.crId);
			}else if(task.codeName == 'bscPrjBonusApply'){
				window.open(linkus.location.rsc + '/02html/prj/new/prjInspire.html?prjId=' + task.prjId);
			}else{
				window.open(linkus.location.rsc + '/02html/prj/prjBasicInfo.html?prjId=' + task.prjId + '&taskId=' + task.id);
			}
		},

		gotoRate: function(task){
			var sf = this;
			sf.hideTodoTasks();
			var url = '/02html/prj/PmRaterScore.html?id=' + task.id;
			if(task.isPending){
				url+='&isPending=true'
			}
			window.open(linkus.location.rsc + url);
		},

		getPrjInfoManagePrivilege : function() {
			var sf = this;
			$.ajax({
				url: linkus.location.prj + '/prjInfoCtrl/hasPrjInfoManagePrivilege.action',
				async: false,
				type: 'post',
				success: function (hasPrivilege) {
					if(hasPrivilege) {
						return ;
					}
					for(var i = 0; i < sf.menuList.length; i++) {
						if(sf.menuList[i].id == '10') {
							sf.menuList.splice(i,1);
						}
					}
				}
			});
		},
		handleOpen: function(){
			var sf = this;
            sf.visible = !sf.visible;
			sf.queryValueItemCount();
			sf.loadTotalTodoTasks();
            sf.loadTodoTasks();
            sf.getPrjLevelToDoCount();
			sf.loadTodoRate();
        },
        handleClose: function(){
        	var sf = this;
        	sf.visible = false;
        },
        getUrlQueryString: function(name) {
    	    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    	    var r = window.location.search.substr(1).match(reg);
    	    if (r != null) return decodeURIComponent(r[2]);
    	    return null;
    	},
    	openHelpDoc: function(){
			window.open(linkus.location.rsc + '/02html/prj/PmsOnlineHelpDocument.html');
    	},

		// BU权限
		queryBUAdminPower	:	function() {
			var sf = this;
			$.ajax({
				type	:	"get",
				url		: 	linkus.location.abp + '/role/bu/admin',
				headers: {
					'Content-Type': 'application/json;charset=utf-8'
				},
				success: function(data) {
					var isBU = data.data;
					if(isBU) {
						for(var i = 0; i < sf.menuList.length; i++) {
							if(sf.menuList[i].id == '7') {
								sf.menuList[i].subMenuList.push({
									id: '7-7', load: false, display: false, name: '预算费率配置', icon: 'ios-list-outline',
									url: linkus.location.rsc + '/02html/prj/abpFRateVersion.html'
								});
							}
						}
					}
				},
				error	: 	function(text) {
					sf.$Message.warning('查询权限出错,请联系管理员!');
				}
			});
		},

		getPrjLevelToDoCount:function() {
			var sf = this;
			$.ajax({
				type:'get',
				url:linkus.location.prj + '/prjLevelApprovalCtr/getPrjLevelToDoCount.action',
				success: function(data) {
					sf.gradedApplyCount = data.apply;
					sf.gradedUnGradeCount = data.unGrade;
				},
				error	: 	function(text) {
					sf.$Message.warning('查询权限出错,请联系管理员!');
				}
			});
		},
		gotoGrad:function (type){
			window.open('/02html/prj/new/projectGradingManagement.html?type='+type);
		},

		//根据关键字过滤待办任务
		filterTodoTasksByKey: function() {
			var sf = this;
			sf.todoTasks = sf.todoBaseTasks.filter(function (item,i){
				var flag = false;
				if(item.name && item.name.indexOf(sf.todoTasksKey) > -1){
					flag = true;
				}
				if(item.submitUserName && item.submitUserName.indexOf(sf.todoTasksKey) > -1){
					flag = true;
				}

				return flag;
			});
			sf.spinShow = false;
		},

		loadTotalTodoTasks: function(){
			var sf = this;
			$.ajax({
				url: linkus.location.prj + '/prjSflTask/getMyTodoTask.action',
				type: 'post',
				dataType: 'JSON',
			}).done(function (data) {
				if(!!data && JSON.stringify(data) != "{}") {
					data = data || [];
				}else {
					data = [];
				}
				if(sf.currentUser.loginName === 'cangjy'){
					sf.loadTotalTodoTasksPending();
				}
				sf.loadTotalTodoTaskNum = data.length;
			});
		},
		loadTotalTodoTasksPending:function(){
			var sf = this;
			$.ajax({
				url: linkus.location.prj+'/prjCapabilityAssessCtrl/getAwaitCheckInfo.action',
				success: function (data) {
					if(!!data && JSON.stringify(data) != "{}") {
						data =  data || [];
					}else {
						data = [];
					}
					sf.loadTotalTodoTaskNum += data.length;
				},
				error: function(data) {
				}
			});
		},
		queryBuAndQaAccess: function(){
			var sf = this;

			$.ajax({
				url: linkus.location.prj + '/prjProcPfm/checkPrjProcPfmsForEdit',
				type: "post",
				headers:{'Content-Type' : 'application/json;charset=utf-8'},
				success: function(res) {
					sf.buAndQaAccess = res.data;
					if(res.data){
						for(var i = 0; i < sf.menuList.length; i++) {
							if(sf.menuList[i].id == '11') {
								sf.menuList[i].subMenuList.push({
									id: '11-19', load: false, display: false, name: 'QA任务管理', icon: '',
									url: linkus.location.rsc + '/02html/prj/qaTaskManage.html'
								});
							}
						}
					}
				},
				error: function(res) {
				}
			});
		},
	},
    computed: {
		pageList: function () {
			var pageList = [];
			this.menuList.forEach(function (item) {
				if (item.hasSubMenu) {
					item.subMenuList.forEach(function (subItem) {
						pageList.push(subItem);
					});
				} else {
					pageList.push(item);
				}
			});
			return pageList;
		}
	},
    created : function () {
    	this.loadTotalTodoTasks();
		this.getPrjLevels();
		this.getScreenToken();

    	this.loadCurrentUser();
		this.queryValueItemCount();
		this.loadTodoTasks();
		this.getPrjLevelToDoCount();
		this.loadTodoRate();
		this.getPrjInfoManagePrivilege();

		this.queryBuLeadership();
		Vue.evtHub = new Vue();
		Vue.evtHub.paraPool = {};

		var noticeShowScript = $('<script src="/00scripts/notice/noticeShow.js"><//script>');
		noticeShowScript.appendTo($("head"));
	},
    mounted: function () {
    	var sf = this;
    	Vue.evtHub.$on("prj-info-modal", function (data) {
			sf.prjInfoModal = data.prjInfoModal;
		});
		sf.updateJWTToken();
		setInterval(function () {
			sf.updateJWTToken();
		}, 86400000);
		sf.getMyQurs();
		sf.addToBizViewEventListener();
		sf.querySysAdmin();
		sf.queryBUAdminPower();
    },
	watch:{
		taskLodingOut:function (newVal,oldVal) {
			var sf =this;
			if(newVal && sf.rateLoadingOut){
				sf.todoTasksNum = sf.todoTasks.length + sf.todoRate.length + sf.myTodoApply.length
			}
		},
		rateLoadingOut:function (newVal,oldVal) {
			var sf =this;
			if(newVal && sf.taskLodingOut){
				sf.todoTasksNum = sf.todoTasks.length + sf.todoRate.length + sf.myTodoApply.length
			}
		},
		todoTasksKey:function (n,o) {
			var sf = this;
			sf.filterTodoTasksByKey();
		},

		showDrawer:function(newVal,oldVal){
			var sf = this;
			sf.$parent.showDrawer = newVal;
		}
	}
});
