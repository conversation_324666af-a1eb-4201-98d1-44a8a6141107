<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目激励</title>

    <!-- 兼容ie -->
    <script src="../../../00scripts/00lib/ie/browser.js"></script>

    <!-- 本地样式 -->
    <link href="../../../01css/style.css" rel="stylesheet" type="text/css"/>
    <link href="../../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>
    <link href="../../../01css/reset.css" rel="stylesheet" type="text/css"/>

    <!-- jQuery -->
    <script src="../../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
    <script src="../../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
    <script src="../../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>

    <!-- VUE-->
    <script src="../../../00scripts/00lib/vue/vue.min.js"></script>

    <!-- iview -->
    <script src="../../../00scripts/00lib/iview/4.4.0/iview.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../00scripts/00lib/iview/4.4.0/styles/iview.css"/>

    <!-- iconfont字体图标 -->
    <link href="../../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../../01css/font/iconfont.js"></script>

    <!-- 本地路由 -->
    <script src="../../../00scripts/location/location.js" type="text/javascript"></script>
    <script src="../../../00scripts/location/verifyLogin.js"></script>
    <script src="../../../00scripts/public/public.js"></script>

    <!-- 本页面使用 -->
    <script src="../../../00scripts/prj/menu/prjHeader.js" type="text/javascript"></script>
    <link href="../../../00scripts/prj/menu/prjHeader.css" rel="stylesheet" type="text/css"/>
    <link href="../../../01css-Prj/style.css" rel="stylesheet" type="text/css"/>

    <script src="../../../00scripts/00lib/vue/loading/vue-infinite-loading.js"></script>
    <script src="../../../00scripts/00lib/vue/loading/vue-sysc-opt.js" type="text/javascript" ></script>
    <script src="../../../00scripts/00lib/vue/loading/lks-prj-loading.js"></script>
    <script type="text/javascript" src="../../../00scripts/00lib/utils/ajax.js"></script>

    <script src="../../../00scripts/prj/lks-prj-load-fuzzy.js" type="text/javascript"></script>

    <script src="../../../00scripts/user/lks-user-load-fuzzy.js" type="text/javascript"></script>
    <script src="../../../00scripts/user/lks-srduser-load-fuzzy.js" type="text/javascript"></script>

    <!-- echarts -->
    <script src="../../../00scripts/00lib/echarts3/echarts.js"></script>
    <script src="../../../00scripts/00lib/echarts3/macarons.js"></script>
    <script src="../../../00scripts/public/provinces.js"></script>
    <script src="../../../00scripts/user/prj-user-load-fuzzy-table.js"></script>

    <script type="text/javascript" src="../../../common/filterTable.js"></script>
    <script type="text/javascript" src="../../../common/defConst.js"></script>

    <!-- 水印 -->
    <script src="../../../00scripts/00lib/watermark/watermark.js" type="text/javascript"></script>

    <script src="../../../00scripts/00lib/html2canvas/html2canvas.js"></script>

    <style>
        html{
            overflow: hidden;
        }
        .prj-item-choose{
            top: 122px;
        }
        .prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item label{
            width: 120px;
        }
        .prj_ben_warp .prj_ben_con .prj_ben_left.expand{
            width: 100%;
            margin-right: 0;
        }

        /* 此样式为filterTable.js使用,隐藏其表头的水平滚动条 */
        .headClass .ivu-table-body {
            overflow: hidden !important;
        }

        .new_table .headClass .ivu-table-stripe .ivu-table-header tr:first-child{
            background: red !important;
        }
        .new_table .headClass .ivu-table-stripe .ivu-table-header tr:first-child th[colspan = '2'],
        .new_table .headClass .ivu-table-stripe .ivu-table-header tr:first-child th[colspan = '3']{
            border-bottom-color: transparent !important;
        }

        .new_table.noSubmit .dataClass .ivu-table td .ivu-table-cell .ivu-input,
        .new_table.noSubmit .dataClass .ivu-table th .ivu-table-cell .ivu-input,
        .new_table.noSubmit .dataClass .ivu-table td .ivu-table-cell .ivu-input-number,
        .new_table.noSubmit .dataClass .ivu-table th .ivu-table-cell .ivu-input-number,
        .new_table.noSubmit .dataClass .ivu-table th .ivu-table-cell .ivu-input-number-input{
            border: 1px solid #3883e5 !important;
            box-shadow: none !important;
        }

        .new_table.noSubmit .dataClass .ivu-table td .ivu-table-cell .ivu-input,
        .new_table.noSubmit .dataClass .ivu-table th .ivu-table-cell .ivu-input,
        .new_table.noSubmit .dataClass .ivu-table td .ivu-table-cell .ivu-input-number,
        .new_table.noSubmit .dataClass .ivu-table th .ivu-table-cell .ivu-input-number,
        .new_table.noSubmit .dataClass .ivu-table td .ivu-table-cell .ivu-select-selection,
        .new_table.noSubmit .dataClass .ivu-table th .ivu-table-cell .ivu-select-selection,
        .new_table.noSubmit .dataClass .ivu-table th .ivu-table-cell .ivu-input-number-input{
            border: 1px solid #3883e5 !important;
            box-shadow: none !important;
        }


        .new_table .ivu-table td .ivu-table-cell .ivu-input, .new_table .ivu-table th .ivu-table-cell .ivu-input, .new_table .ivu-table td .ivu-table-cell .ivu-input-number, .new_table .ivu-table th .ivu-table-cell .ivu-input-number, .new_table .ivu-table th .ivu-table-cell .ivu-input-number-input{
            border: 1px solid #dcdee2 !important;
            box-shadow: none !important;
        }

        .new_table .ivu-table td .ivu-table-cell .ivu-input, .new_table .ivu-table th .ivu-table-cell .ivu-input, .new_table .ivu-table td .ivu-table-cell .ivu-input-number, .new_table .ivu-table th .ivu-table-cell .ivu-input-number, .new_table .ivu-table td .ivu-table-cell .ivu-select-selection, .new_table .ivu-table th .ivu-table-cell .ivu-select-selection, .new_table .ivu-table th .ivu-table-cell .ivu-input-number-input{
            border: 1px solid #dcdee2 !important;
            box-shadow: none !important;
        }

        .prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item > div{
            width: calc(100% - 120px);
        }

        .his_tab .ivu-steps-status-process .ivu-steps-head-inner span{
            color: #2d8cf0 !important;
        }

        .prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot{
            min-height: calc(100% - 68px);
            position: relative;
        }

        .prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .fold_unfold_wrap{
            position: absolute;
            width: 12px !important;
            height: 78px;
            right: -12px;
            background: url('../../../03images/PMS/home-telescoping.png');
            background-size: cover;
            top: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .fold_unfold_wrap.expand{
            right: 0;
            transform: rotate(180deg);
        }

        .prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .fold_unfold_wrap i {
            font-size: 12px;
            color: #fff;
            transform: rotate(0);
        }

        .showDrawer .mask_div{
            color: #3f3f3f !important;
        }


        .dataClass .ivu-table-body::-webkit-scrollbar{
            width: 0px !important;
        }

        .t-edit-warp{
            display: flex;
            align-items: center;
        }

        .t-edit-warp span{
            width: calc(100% - 20px);
        }

        .t-edit-warp i{
            width: 20px;
            color:#3883e5;
            cursor: pointer;
        }

        .actionWarp .ivu-btn span{
            color:#3883e5 !important;
        }

        .view_modal_table .ivu-table td,
        .view_modal_table .ivu-table th{
            border-bottom: 1px solid #e8eaec !important;
            border-left: 1px solid #dddee1 !important;
        }

        .view_modal_table .ivu-table td[rowspan="4"],
        .view_modal_table .ivu-table td[rowspan="5"],
        .view_modal_table .ivu-table td[rowspan="9"],
        .view_modal_table .ivu-table td[rowspan="7"],
        .view_modal_table .ivu-table td[rowspan="8"],
        .view_modal_table .ivu-table th:first-child{
            border-left: 1px solid transparent !important;
        }

        .view_modal_table .ivu-table .ivu-table-tbody tr:last-child td,
        .view_modal_table .ivu-table td[rowspan="7"]{
            border-bottom: 1px solid transparent !important;
        }

        .desc-modal-warp .ivu-modal{
            top:20px;
        }
        .desc-modal-warp-title >span{
            color: #A5ACC4;
            margin-right: 8px;
            cursor: pointer;
        }
        .desc-modal-warp-title >span.active{
            color: #3883e5;
        }
        .inspire_desc_table .ivu-table td:first-child, .inspire_desc_table .ivu-table th:first-child{
            border-left: 1px solid transparent !important;
        }
        .prj_ben_right.is_hide{
            display: none;
        }
        .unfold_btn_wrap .approve_btn .ivu-btn{
            min-width: 92px;
        }
    </style>
</head>
<body id="watermarkWarp">
<div class="prj_ben_warp header_fix" id="main" v-cloak>
    <Spin size="large" fix v-if="initPrjLoading">
        <div style="text-align:center; padding: 40px 0;">
            <i class="ivu-icon ivu-icon-ios-loading ivu-load-loop" style="font-size:32px;color:#2d8cf0;vertical-align:middle;margin-right:10px;"></i>
            <span style="font-size:16px;color:#666;">正在加载项目绩效，请稍候...</span>
        </div>
    </Spin>
    <prj-header></prj-header>
    <div class="prj_ben_con"  ref="scrollview">
        <div class="prj_ben_left" :class="{ expand: isExpand }">
            <div class="prj_b_l_top">
                <div class="ben_top_filter">
                    <div class="t_col_1">
                        <label>项目</label>
                        <lks-prj-loading :is-prj-set="isPrjSet" :prj-name="prjName" :is-include-member="true"></lks-prj-loading>
                    </div>

                    <div class="t_col_3">
                        <div  @click="viewMoneyModal = true" v-if="(pfmType === 'approve' || pfmType === 'pmApprove') && (getCheckEmp() || isPfmAdmin || isViewLeader)  " style="margin-right: 12px;">
                            <i class="iconfont icon-yulan"></i>
                            查看金额
                        </div>

                        <div  @click="updateModal" v-if="isPfmAdmin && (pfmType === 'start' || pfmType === 'submit' || (pfmApply.status && pfmApply.status.cid === '6821c3defb03e0e1732c146b'))"  style="margin-right: 12px;">
                            <i class="iconfont icon-shuru"></i>
                            更新
                        </div>

                        <div  @click="exportExcel()" v-if="isPfmAdmin || isViewLeader">
                            <i class="iconfont icon-export"></i>
                            导出
                        </div>

                        <div class="unfold_btn_wrap" style="margin-left: 12px;" v-if="isExpand">
                            <div class="approve_btn">
                                <div class="one_btn" :class="{'two_btn':pfmType === 'approve'}" v-if="isPm || isApproveUser || isPfmAdmin || getCheckEmp()">
                                    <!-- 初始化-->
                                    <i-Button type="primary" v-if="isPfmAdmin && !pfmType" @click="initPrjModal = true">发起绩效评分</i-Button>
                                    <i-Button type="primary" v-if="pfmType === 'start' && isPm" @click="startPrjPfm">启动绩效评分</i-Button>
                                    <i-Button type="primary" v-if="pfmType === 'submit'  && isPm" @click="startPrjPfm">提交绩效评分</i-Button>

                                    <i-Button type="primary" v-if="pfmType === 'approve'  && getCheckEmp()" @click="approveModal = true;approveType = 'pass'" class="button">{{approvePass}}</i-Button>
                                    <i-Button type="primary" v-if="pfmType === 'approve'  && getCheckEmp()" @click="approveModal = true;approveType = 'reject'" class="button">{{approveReject}}</i-Button>

                                    <i-Button type="primary" v-if="getCheckEmp() && pfmType === 'pmApprove'" @click="startPrjPfm" class="button">提交实际激励分配</i-Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="prj_b_l_bot flex" v-if="!prjEmpPfmApplyLoading && prjEmpPfmApplyList.length === 0 && !pfmType">
                <div class="fold_unfold_wrap" :class="{ expand: isExpand }" @click="isExpand = !isExpand">
                    <i class="iconfont icon-rightArrow"></i>
                </div>

                <div class="l_tip" >请先发起绩效评分!</div>
            </div>

            <div class="prj_b_l_bot flex" v-show="!prjEmpPfmApplyLoading && !prjPerformanceLoading && (prjEmpPfmApplyList.length > 0 && ((pfmType === 'submit' && prjPerformanceData.length === 0) || (pfmType != 'submit' && !isPfmAdmin && !isViewLeader && !getCheckEmp())))">
                <div class="fold_unfold_wrap" :class="{ expand: isExpand }" @click="isExpand = !isExpand">
                    <i class="iconfont icon-rightArrow"></i>
                </div>

                <div class="l_tip" v-if="prjEmpPfmApplyList.length > 0 && !isPfmAdmin && !isViewLeader">您暂无项目绩效&激励查看权限！</div>
            </div>





            <div class="prj_b_l_bot" id="prj_b_l_bot"  :style="{minHeight:tabHeight}" ref="prjlBot" v-show="prjEmpPfmApplyList.length > 0 && ((isPfmAdmin || isViewLeader || getCheckEmp()) || (pfmType === 'submit' && prjPerformanceData.length > 0))">
                <div class="fold_unfold_wrap" :class="{ expand: isExpand }" @click="isExpand = !isExpand">
                    <i class="iconfont icon-rightArrow"></i>
                </div>

                <div class="b_base_info" v-show="pfmType === 'approve'  && !isShowResult">
                    <div>
                        <div class="prj_b_title">
                            基本信息
                        </div>
                        <div class="prj_b_b_filter col_3">
                            <div class="fol_item">
                                <label>项目集名称</label>
                                <div class="span_ellipsis">
                                    <span>{{prjName}}</span>
                                    -
                                    <em>{{prjBasicInfo.level}}</em>
                                </div>
                            </div>
                            <div class="fol_item  pdL20">
                                <label>归属省份</label>
                                <div class="span_ellipsis">
                                    <span>{{prjBasicInfo.province}}</span>
                                </div>
                            </div>

                            <div class="fol_item  pdL20">
                                <label>项目经理</label>
                                <div class="span_ellipsis">
                                    <span>{{prjBasicInfo.pmUser}}</span>
                                </div>
                            </div>

                            <div class="fol_item ">
                                <label>激励周期</label>
                                <div class="span_ellipsis">
                                    <span>{{ prjBasicInfo.incentivePeriod }}</span>
                                </div>
                            </div>
                            <div class="fol_item pdL20">
                                <label>项目考评成绩</label>
                                <div class="span_ellipsis">
                                    <span>{{ pfmApply.pmsEvalScore || 0 }}</span>
                                </div>
                            </div>

                            <div class="fol_item pdL20">
                                <label>资源实际(人月)</label>
                                <div class="span_ellipsis">
                                    <span>{{ pfmApply.mm }}</span>
                                </div>
                            </div>
                            <div class="fol_item">
                                <label>不参与激励<br>分配工时(人月)</label>
                                <div class="span_ellipsis">
                                    <span>{{ pfmApply.notPfmMm }}</span>
                                </div>
                            </div>
                            <div class="fol_item pdL20">
                                <label>调整工时(人月)</label>
                                <div class="span_ellipsis">
                                    <Input-Number v-if="getCheckEmp() && !isEncryption"  v-model="pfmApply.prePrjMm" @on-blur="updatePrjEmpPfmApply"></Input-Number>
                                    <span v-else>{{ pfmApply.prePrjMm }}</span>
                                </div>
                            </div>
                            <div class="fol_item pdL20">
                                <label>参与激励(人月)</label>
                                <div class="span_ellipsis">
                                    <span>{{ pfmApply.pfmMm }}</span>
                                </div>
                            </div>
                            <div class="fol_item">
                                <label>应发项目激励(元)</label>
                                <div class="span_ellipsis">
                                    <span>{{ pfmApply.bonusDue }}</span>
                                </div>
                            </div>
                            <div class="fol_item pdL20">
                                <label>特批额度(元)</label>
                                <div class="span_ellipsis">
                                    <Input-Number v-if="getCheckEmp() && !isEncryption"  v-model="pfmApply.bonusExtra" @on-blur="updatePrjEmpPfmApply"></Input-Number>
                                    <span v-else>{{ pfmApply.bonusExtra }}</span>
                                </div>
                            </div>

                            <div class="fol_item pdL20">
                                <label>项目激励总额(元)</label>
                                <div class="span_ellipsis">
                                    <span>{{ pfmApply.bonusActual }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="result_info" v-if="pfmType === 'approve'  && isShowResult">
                    <div>
                        <div class="prj_b_title" style="margin-bottom: 16px;">
                            结果分析
                        </div>
                        <div class="result_info_list">

                            <div class="result_info_item col3">
                                <div class="result_info_item_col">
                                    <div class="result_info_item_col_title">
                                        计划奖金
                                    </div>
                                    <div class="result_info_item_col_content new_table_warp no_border">
                                        <i-table stripe :border="true" class="new_table" :columns="planBonusColumns" :data="planBonusData"></i-table>
                                    </div>
                                </div>

                                <div class="result_info_item_col">
                                    <div class="result_info_item_col_title">
                                        实际发放
                                    </div>
                                    <div class="result_info_item_col_content new_table_warp no_border">
                                        <i-table stripe :border="true" class="new_table" :columns="actualBonusColumns" :data="actualBonusData"></i-table>
                                    </div>
                                </div>

                                <div class="result_info_item_col">
                                    <div class="result_info_item_col_title">
                                        奖金分析
                                    </div>
                                    <div class="result_info_item_col_content new_table_warp no_border">
                                        <i-table stripe :border="true" class="new_table" :columns="bonusAnalysisColumns" :data="bonusAnalysisData"></i-table>
                                    </div>
                                </div>

                            </div>

                            <div class="result_info_item">
                                <div class="result_info_item_col">
                                    <div class="result_info_item_col_title">
                                        按归属组、角色奖金分布情况
                                    </div>
                                    <div class="result_info_item_col_content new_table_warp no_border">
                                        <i-table stripe :border="true" class="new_table" :columns="distributionColumns1" :data="distributionData1"></i-table>
                                    </div>
                                </div>
                            </div>

                            <div class="result_info_item">
                                <div class="result_info_item_col">
                                    <div class="result_info_item_col_title">
                                        按部门、角色奖金分布情况
                                    </div>
                                    <div class="result_info_item_col_content new_table_warp no_border">
                                        <i-table stripe :border="true" class="new_table" :columns="distributionColumns2" :data="distributionData2"></i-table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="b_base_info">
                    <div class=notional_pooling>
                        <div class="prj_b_title prj_value_title">
                            项目人员绩效
                            <span @click="showDescModal = true" style="color: rgb(56, 131, 229); font-size: 12px; margin-left: 12px; cursor: pointer;">查看说明</span>

                            <div class="actionWarp" v-if="isPm && (pfmType === 'start' || pfmType === 'submit' || pfmType === 'pmApprove')" style="display:flex;align-items: center">
                                <span v-if="pfmType === 'pmApprove'" @click="copyBonusDueToActual()" style="margin-left: 4px">
                                    <i class="iconfont icon-copy"></i>
                                        复制计划金额
                                    </span>

                                <span @click="addUser" style="margin-left: 4px">
                                        <i class="iconfont icon-newDocument"></i>
                                        新增
                                    </span>
                                <span @click="deleteUser" style="margin-left: 4px">
                                        <i class="iconfont icon-ashbin"></i>
                                        删除
                                    </span>
                            </div>

                            <div  class="actionWarp" v-if="!isPm && !isPfmAdmin && pfmType === 'submit' && !isViewLeader">
                                <i-Button style="width: 80px;border-color: #3883e5" @click="editTableAll" size="small">保存</i-Button>
                            </div>
                        </div>


                        <div class="prj_b_b_filter col_3" v-if="pfmType === 'approve' || pfmType === 'pmApprove'">
                            <div class="fol_item">
                                <label>计划应发金额(元)</label>
                                <div class="span_ellipsis">
                                    <span>{{pfmApply.bonusActual}}</span>
                                </div>
                            </div>
                            <div class="fol_item  pdL20">
                                <label>实际应发金额(元)</label>
                                <div class="span_ellipsis">
                                    <span>{{isEncryption ? '****' : bonusActual}}</span>
                                </div>
                            </div>

                            <div class="fol_item  pdL20">
                                <label>剩余待分配金额(元)</label>
                                <div class="span_ellipsis">
                                    <span>{{isEncryption ? '****' : pfmApply.bonusActual - bonusActual}}</span>
                                </div>
                            </div>


                        </div>

                        <div class="new_table_warp no_border" :class="{'noSubmit' : pfmType != 'submit'}">
                            <filter-table
                                    :height="tableHeight"
                                    :data="prjPerformanceData1"
                                    :columns="prjPerformanceColumns"
                                    :select-data-map="selectDataMap"
                                    class="lineClamp2 new_table"
                                    :loading="prjPerformanceLoading"
                                    :disable-dblclick="true"
                            ></filter-table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="prj_ben_right" :class="{ is_hide: isExpand }">
            <div class="approve_his_desc" ref="hisDesc" v-if="turnDownInfo">
                <span>审批意见：</span>
                {{turnDownInfo.auditDesc}}
            </div>

            <div class="approve_warp" :class="{no_show:isShowHis}">
                <div class="approve_btn" >
                    <div class="one_btn" :class="{'two_btn':pfmType === 'approve'}" v-if="isPm || isApproveUser || isPfmAdmin || getCheckEmp()">
                        <!-- 初始化-->
                        <i-Button type="primary" v-if="isPfmAdmin && !pfmType" @click="initPrjModal = true;updatePrj = false">发起绩效评分</i-Button>
                        <i-Button type="primary" v-if="pfmType === 'start' && isPm" @click="startPrjPfm">启动绩效评分</i-Button>
                        <i-Button type="primary" v-if="pfmType === 'submit'  && isPm" @click="startPrjPfm">提交绩效评分</i-Button>

                        <i-Button type="primary" v-if="pfmType === 'approve'  && getCheckEmp()" @click="approveModal = true;approveType = 'pass'" class="button">{{approvePass}}</i-Button>
                        <i-Button type="primary" v-if="pfmType === 'approve'  && getCheckEmp()" @click="approveModal = true;approveType = 'reject'" class="button">{{approveReject}}</i-Button>

                        <i-Button type="primary" v-if="getCheckEmp() && pfmType === 'pmApprove'" @click="startPrjPfm" class="button">提交实际激励分配</i-Button>
                    </div>
                </div>
                <ul>
                    <li :class="item.nodeStatus" v-for="item in apprStatusFlow">
                        <i></i>
                        {{item.status.name}}
                    </li>
                </ul>
            </div>

            <div class="his_btn" v-if="!isShowHis && auditHists.length > 0" @click="isShowHis = true;getHisHeight()">
                审批历史
            </div>



            <div class="approve_his_warp" v-if="isShowHis">
                <div class="approve_his his_tab" :style="{height:hisHeight}">
                    <div class="prj_b_title">
                        审批历史 <i class="iconfont icon-back" @click="isShowHis = false"></i>
                    </div>
                    <div class="approve_his_step">
                        <steps :current="currentStep" direction="vertical" size="small">
                            <step v-for="(h, index) in auditHists" style="width: 100%" :title="h.rcdStatus.name"
                                  :icon="getIcon(h.nodeCodeName)" :class="h.nodeCodeName === 'pending' ? 'current' : ''">
                                <div class="ivu-steps-content" slot="content">
                                    <div v-if="(!!h.nodeCodeName && (h.nodeCodeName === 'isApproved' || h.nodeCodeName === 'isFailed'))"
                                         class="selectA">
                                            <span class="content" style="padding:12px 0 8px;display: inline-block;">
                                                {{ h.auditUser ? h.auditUser.userName : "" }}/{{ h.auditUser ? h.auditUser.loginName : "" }}操作于{{ h.auditTime }}
                                            </span>
                                        <span v-if="!!h.auditDesc" class="content" style="color:#FF9900">
                                                    <br />
                                                审批意见：{{h.auditDesc }}
                                            </span>
                                    </div>
                                    <span v-if="!!h.nodeCodeName || h.nodeCodeName === 'pending'" class="content" style="overflow: hidden;display: inline-block;text-overflow: ellipsis;white-space: nowrap;width: 100%;">
                                            {{h.resUserLogin }}
                                        </span>
                                </div>
                            </step>
                        </steps>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <Modal v-model="initPrjModal" title="发起项目绩效&激励" >
        <i-form ref="initPrjData" class="view_modal_form" :model="initPrjData" :rules="ValidateRules" label-position="left" :label-width="120">
            <Form-item label="项目周期开始月" prop="startMonth">
                <Date-Picker v-model="initPrjData.startMonth"  type="month" placeholder="选择项目周期开始月" :options="startOptions" style="width: 100%;"></Date-Picker>
            </Form-item>

            <Form-item label="项目周期结束月" prop="endMonth">
                <Date-Picker v-model="initPrjData.endMonth"  type="month" placeholder="选择项目周期结束月" :options="endOptions" style="width: 100%;"></Date-Picker>
            </Form-item>


            <Form-item label="项目地" prop="projectLocation">
                <i-Select v-model="initPrjData.projectLocation" placeholder="请选择项目地" multiple filterable style="width: 100%;">
                    <Option-Group :label="item.label" v-for="item in provinces" :key="item.value">
                        <i-Option v-for="item in item.children" :value="item.value" :key="item.value">{{ item.label }}</i-Option>
                    </Option-Group>
                </i-Select>
            </Form-item>
        </i-form>
        <div slot="footer">
            <i-button type="text" @click="initPrjModal = false;updatePrj = false;">取消</i-button>
            <i-button type="primary" @click="initPrj" :disabled="initPrjLoading">确定</i-button>
        </div>
    </Modal>


    <Modal v-model="addUserModal" title="新增人员" >
        <i-form ref="addUserData" class="view_modal_form" :model="addUserData" :rules="ValidateRules" label-position="left" :label-width="120">
            <Form-item label="人员" prop="userList">
                <i-Select v-model="addUserData.userList" multiple filterable :remote-method="userRemoteMethod" :loading="userLoading" placeholder="请选择人员" style="width: 100%;">
                    <i-Option v-for="item in userList" :value="item.id" :key="item.id">{{ item.userName+'/'+item.loginName }}</i-Option>
                </i-Select>
            </Form-item>
        </i-form>

        <div slot="footer">
            <i-button type="text" @click="addUserModal = false">取消</i-button>
            <i-button type="primary" :disabled="isAddUserLoading" @click="insertPrjBonus">确定</i-button>
        </div>
    </Modal>


    <Modal v-model="approveModal" title="审批" >
        <i-form class="view_modal_form" label-position="left" :label-width="120">
            <Form-item label="审批意见" >
                <i-Input v-model="auditDesc" type="textarea" :rows="4" placeholder="请输入审批意见"></i-Input>
            </Form-item>


            <Form-item label="是否加审" v-if="showAdditionalReview && approveType === 'pass'">
                <Radio-Group v-model="isAdditionalReview">
                    <Radio :label="0">
                        <span>加审</span>
                    </Radio>
                    <Radio :label="1">
                        <span>不加审</span>
                    </Radio>
                </Radio-Group>
            </Form-item>


        </i-form>
        <div slot="footer">
            <i-button type="text" @click="approveModal = false">取消</i-button>
            <i-button type="primary" @click="approvePfm">确定</i-button>
        </div>
    </Modal>


    <Modal v-model="viewMoneyModal" title="身份认证" >
        <i-form class="view_modal_form" label-position="left" :label-width="120">
            <Form-item label="VPN口令">
                <i-Input v-model="vpnPwd"  placeholder="请输入“信部落”VPN动态口令"></i-Input>
            </Form-item>
        </i-form>
        <div slot="footer">
            <i-button type="text" @click="viewMoneyModal = false">取消</i-button>
            <i-button type="primary" @click="authenticateUser">确定</i-button>
        </div>
    </Modal>



    <Modal v-model="showDescModal" width="1000" class="desc-modal-warp">
        <div class="desc-modal-warp-title" slot="header">
            <span :class="{ active: descTitle === 'detailsDesc' }" @click="descTitle = 'detailsDesc'">详细说明</span>
            <span :class="{ active: descTitle === 'inspireConstraint' }" @click="descTitle = 'inspireConstraint'">激励约束</span>
        </div>
        <div v-if="descTitle === 'detailsDesc'">
            <i-Table class="view_modal_table" border :height="descTableHeight" :columns="showDescColumns" :data="showDescData" :span-method="handleSpan"></i-Table>
        </div>
        <div v-if="descTitle === 'inspireConstraint'">
            <i-Table class="view_modal_table inspire_desc_table" border :columns="showDescInspireColumns" :data="showDescInspireData"></i-Table>
        </div>
        <div slot="footer">
        </div>
    </Modal>

    <iframe :src="exportUrl" style="width: 100%; height: 100%;display: none;"></iframe>
</div>


<script>
    Date.prototype.format = function (fmt) {
        var o = {
            "M+": this.getMonth() + 1,                 //月份
            "d+": this.getDate(),                    //日
            "h+": this.getHours(),                   //小时
            "m+": this.getMinutes(),                 //分
            "s+": this.getSeconds(),                 //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds()             //毫秒
        };
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (var k in o) {
            if (new RegExp("(" + k + ")").test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            }
        }
        return fmt;
    };

    //控制登录失效后HTML页面跳转登录页
    verifyLogin();
    Vue.evtHub  = new Vue();
    var vue=new Vue({
        el: '#main',
        data: function(){
            var sf = this;
            return {
                isPrjSet: true,
                prjName: '',
                prjId: '',
                prjCode: '',
                prjIdValue: '',
                selectPrjSbuId: '',
                tabHeight: '400px',

                prjEmpPfmApplyLoading:true,

                hisHeight:'200px',
                approveModal:false,
                approveType:'', //reject 驳回 pass 通过

                auditHists:[],
                isShowHis:false,
                auditDesc:'',
                turnDownInfo:null,

                initPrjModal:false,

                exportUrl:'',

                // 项目绩效申请列表
                prjEmpPfmApplyList:[],
                apprStatusFlow:[],

                currentStepCheckEmp:[],

                updatePrj:false,

                showDescModal:false,
                descTableHeight:250,
                showDescColumns:[
                    {
                        title: '分类',
                        key: 'classification',
                        width:120,
                    },
                    {
                        title: '列字段',
                        key: 'fields',
                        width:120,
                    },
                    {
                        title: '类型',
                        key: 'type',
                        width:100
                    },
                    {
                        title: '填写人',
                        key: 'addUser',
                        width:110
                    },
                    {
                        title: '逻辑/计算公式说明',
                        key: 'desc',
                        render: (h, params) => {
                            return h('div',{
                                style:{
                                    whiteSpace:'pre-wrap'
                                }
                            },params.row.desc );
                        }
                    },
                ],
                showDescData:[
                    {
                        classification:'人员基本信息',
                        fields:'人员',
                        type:'初始化同步',
                        addUser:'项目经理',
                        desc:'初始化范围：项目下所有有效填工时正式人员',
                    },
                    {
                        classification:'人员基本信息',
                        fields:'归属部门',
                        type:'自动计算',
                        addUser:'',
                        desc:'人员归属部门',
                    },
                    {
                        classification:'人员基本信息',
                        fields:'岗位',
                        type:'初始化同步',
                        addUser:'项目经理',
                        desc:'根据项目工时角色，映射激励中岗位',
                    },
                    {
                        classification:'人员基本信息',
                        fields:'归属组',
                        type:'初始化同步',
                        addUser:'项目经理',
                        desc:'同步项目工时中组织架构信息',
                    },
                    {
                        classification:'工作绩效&态度',
                        fields:'工作量',
                        type:'初始化同步',
                        addUser:'项目经理',
                        desc:'项目激励周期内，人员在“项目人员绩效”中工作量得分均值，满分100分',
                    },
                    {
                        classification:'工作绩效&态度',
                        fields:'工作质量',
                        type:'初始化同步',
                        addUser:'项目经理',
                        desc:'项目激励周期内，人员在“项目人员绩效”中工作质量得分均值，满分100分',
                    },
                    {
                        classification:'工作绩效&态度',
                        fields:'积极性',
                        type:'手工填写',
                        addUser:'打分人',
                        desc:'打分人填写，满分100分',
                    },
                    {
                        classification:'工作绩效&态度',
                        fields:'沟通能力',
                        type:'手工填写',
                        addUser:'打分人',
                        desc:'打分人填写，满分100分',
                    },
                    {
                        classification:'工作绩效&态度',
                        fields:'吃苦耐劳',
                        type:'手工填写',
                        addUser:'打分人',
                        desc:'打分人填写，满分100分',
                    },
                    {
                        classification:'计算系数',
                        fields:'岗位系数',
                        type:'自动计算',
                        addUser:'',
                        desc:'项目总监、技术总监 4.5、项目经理 4、技术经理 3.5、技术专家、子项目经理、大组长 3、模块组长 2.5、核心骨干 2、普通成员 1',
                    },
                    {
                        classification:'计算系数',
                        fields:'工时时长(天)',
                        type:'自动计算',
                        addUser:'',
                        desc:'工时天数',
                    },
                    {
                        classification:'计算系数',
                        fields:'增补工时(天)',
                        type:'手工填写',
                        addUser:'项目经理',
                        desc:'项目经理填写',
                    },
                    {
                        classification:'计算系数',
                        fields:'增补说明',
                        type:'手工填写',
                        addUser:'项目经理',
                        desc:'项目经理填写',
                    },
                    {
                        classification:'计算系数',
                        fields:'项目时长系数',
                        type:'自动计算',
                        addUser:'',
                        desc:'项目时长 / 项目总时长\n' +
                            '其中 项目时长：工时时长+增补工时；项目总时长：激励周期内项目总工时天数',
                    },
                    {
                        classification:'计算系数',
                        fields:'累计差旅天数',
                        type:'自动计算',
                        addUser:'',
                        desc:'累计差旅天数，根据差旅申请单数据计算',
                    },
                    {
                        classification:'计算系数',
                        fields:'累计差旅次数',
                        type:'自动计算',
                        addUser:'',
                        desc:'累计差旅次数，根据差旅申请单数据计算',
                    },
                    {
                        classification:'计算系数',
                        fields:'平均差旅时长系数',
                        type:'自动计算',
                        addUser:'',
                        desc:'“累计差旅天数/累计差旅次数”，若小于15天系数为0.7，大于等于15天小于25天，系数为0.9，大于等于25天，系数为1\n' +
                            '不出差人员系数为1',
                    },

                    {
                        classification:'计算结果',
                        fields:'基础得分',
                        type:'自动计算',
                        addUser:'',
                        desc:'（工作量*0.5+工作质量*0.5）*0.8 + （积极性*0.5+沟通能力*0.2+吃苦耐劳*0.3）*0.2',
                    },
                    {
                        classification:'计算结果',
                        fields:'个人绩效分',
                        type:'自动计算',
                        addUser:'',
                        desc:'基础考评分*岗位指标系数*项目时长系数*平均差旅时长系数',
                    },
                    {
                        classification:'计算结果',
                        fields:'项目管理系数',
                        type:'自动计算',
                        addUser:'',
                        desc:'根据考评成绩区间范围，取项目管理系数，仅技术总监、项目总监、项目经理、子项目经理、技术经理有该系数\n' +
                            '[95 , 100]  1\n' +
                            '[85 , 95) 0.8\n' +
                            '[75 , 85) 0.6\n' +
                            '[0 , 75) 0',
                    },
                    {
                        classification:'计算结果',
                        fields:'计划发放金额',
                        type:'自动计算',
                        addUser:'',
                        desc:'人员绩效分/ ∑ 人员绩效分 * 应发激励总额\n' +
                            '其中仅“是否参与激励”为是，才纳入计算\n' +
                            '人员绩效分 = 个人绩效分 * 项目管理系数',
                    },
                    {
                        classification:'发放结果',
                        fields:'实际发放金额',
                        type:'手工填写',
                        addUser:'项目经理',
                        desc:'项目经理填写',
                    },
                    {
                        classification:'发放结果',
                        fields:'调整说明',
                        type:'手工填写',
                        addUser:'项目经理',
                        desc:'项目经理填写\n' +
                            '实际发放金额的浮动范围为10%以内，且<1k，若<200,可以调整至200。若超过此范围，说明必填',
                    },
                    {
                        classification:'发放结果',
                        fields:'实际计划偏差',
                        type:'自动计算',
                        addUser:'',
                        desc:'实际发放金额 - 计划发放金额',
                    },
                    {
                        classification:'发放结果',
                        fields:'偏差率',
                        type:'自动计算',
                        addUser:'',
                        desc:'（实际发放金额 - 计划发放金额）/计划发放金额 *100%',
                    },
                    {
                        classification:'项目激励信息',
                        fields:'资源实际(人月)',
                        type:'自动计算',
                        addUser:'',
                        desc:'所有人员工时时长总和',
                    },
                    {
                        classification:'项目激励信息',
                        fields:'不参与激励分配工时(人月)',
                        type:'自动计算',
                        addUser:'',
                        desc:'“是否参与激励”为“否”的人员工时时长总和',
                    },
                    {
                        classification:'项目激励信息',
                        fields:'调整工时(人月)',
                        type:'手工填写',
                        addUser:'项目考评管理员',
                        desc:'项目考评管理员填写',
                    },
                    {
                        classification:'项目激励信息',
                        fields:'参与激励(人月)',
                        type:'自动计算',
                        addUser:'',
                        desc:'资源实际 + 调整工时 - 不参与激励分配工时',
                    },
                    {
                        classification:'项目激励信息',
                        fields:'应发放项目激励',
                        type:'自动计算',
                        addUser:'',
                        desc:'激励总人月数×每人月奖金系数×分级系数×项目考评成绩/100\n' +
                            '分级系数：A+/A：1.2；B/B+：1.0；C/E：0.8；F/G：0.7',
                    },
                    {
                        classification:'项目激励信息',
                        fields:'特批额度',
                        type:'手工填写',
                        addUser:'项目运营总监',
                        desc:'项目运营总监填写',
                    },
                    {
                        classification:'项目激励信息',
                        fields:'项目激励总额',
                        type:'自动计算',
                        addUser:'',
                        desc:'应发项目激励 + 特批额度',
                    },
                ],
                showDescInspireColumns: [
                    {
                        title: '约束对象',
                        key: 'constraintObj',
                        width: 120,
                    },
                    {
                        title: '约束条件说明',
                        key: 'constraintDesc',
                        render: (h, params) => {
                            return h('div',{
                                style:{
                                    whiteSpace:'pre-wrap'
                                }
                            },params.row.constraintDesc );
                        }
                    }
                ],
                showDescInspireData: [
                    {
                        constraintObj: '项目',
                        constraintDesc: '项目绩效低于75分或特殊情况下经营单元不达标无项目激励。'
                    },
                    {
                        constraintObj: '管理人员',
                        constraintDesc: '管理人员（项目总监、技术总监、项目经理，子项目经理，技术经理）的激励按项目绩效成绩打折，如出现项目成本串填等违规事件，出现一次项目管理人员激励折上5折，出现两次以上取消激励。\n' +
                            '项目绩效 激励系数\n' +
                            '【95~100】 1 \n' +
                            '【85~95)  0.8 \n' +
                            '【75~85)  0.6 \n' +
                            '75以下 0',
                    },
                    {
                        constraintObj: '成员',
                        constraintDesc: '项目成员的工作量、工作质量、客户满意度、工作纪律、工作态度等需要满足激励发放门槛\n' +
                            '工作量：人员工作饱和度得分高于60分\n' +
                            '工作质量：人员质量得分高于60分\n' +
                            '客户满意度：客户投诉到项目经理或相关管理人员1次\n' +
                            '工作纪律：无正当理由的迟到、早退3次以上或旷工1次\n' +
                            '工作态度：不服从项目组工作安排2次及以上',
                    },
                ],

                initPrjData:{
                    startMonth:null,
                    endMonth:null,
                    projectLocation:null,
                },

                isPm:false,

                isAdditionalReview:1,
                showAdditionalReview:false,

                // 新增人员
                addUserData:{
                    userName:null,
                },
                userLoading:false,
                userList:[],
                userListBase:[],
                timer:null,

                pfmApplyId:null,

                addUserModal:false,

                currentStep:-1,
                currentUser:null,
                pmUser:null,

                isApproveUser:false,

                isViewLeader:false,
                // 验证规则
                ValidateRules:{
                    startMonth:[
                        { required: true, type: 'date', message: '请选择项目周期开始月', trigger: 'change' }
                    ],
                    endMonth:[
                        { required: true, type: 'date', message: '请选择项目周期结束月', trigger: 'change' }
                    ],
                    projectLocation:[
                        { required: true, type: 'array', message: '请选择项目地', trigger: 'change' }
                    ],
                    userList:[
                        { required: true, type: 'array', message: '请选择人员', trigger: 'change' }
                    ],

                },

                provinces: provinces,

                // 项目周期开始月
                startOptions: {
                    disabledDate (date) {
                        if(!!sf.initPrjData.endMonth){
                            return date && date.valueOf() > new Date(sf.initPrjData.endMonth).valueOf();
                        }
                    }
                },

                // 项目周期结束月
                endOptions: {
                    disabledDate (date) {
                        if(!!sf.initPrjData.startMonth){
                            return date && date.valueOf() < new Date(sf.initPrjData.startMonth).valueOf();
                        }
                    }
                },


                // 表格选项
                tableOpts:{
                    checkPsn:[],
                    title:[],

                },


                tableHeight:null,

                prjPerformanceColumns:[
                    {
                        type: 'selection',
                        width: 40,
                        fixed: 'left'
                    },
                    {
                        title: '序',
                        type:'index',
                        width: 40,
                        fixed: 'left'
                    },
                    {
                        title: '是否参与激励',
                        key: 'isPfmEmp',
                        width: 70,
                        render: (h, params) => {
                            if(sf.isPfmAdmin || sf.isViewLeader){
                                return h('span', params.row.isPfmEmp ? '是' : '否');
                            }else{
                                return sf.renderTable('i-switch', params.row, h, 'isPfmEmp', 'prjPerformance','obj')
                            }
                        },
                        fixed: 'left',
                        filter: {
                            type: 'Select'
                        }
                    },
                    {
                        title: '人员',
                        key: 'emp',
                        width: 100,
                        render: (h, params) => {
                            var emp = params.row.emp;
                            var userName = '';
                            var loginName = '';
                            if(!!emp){
                                userName = emp.userName;
                                loginName = emp.loginName;
                                return h('span', userName+'/'+loginName);
                            }
                        },
                        fixed: 'left',
                        filter: {
                            type: 'Input'
                        }
                    },
                    {
                        title: '人员类型',
                        key: 'employeeType',
                        width: 80,
                        fixed: 'left',
                        filter: {
                            type: 'Select'
                        }
                    },
                    {
                        title: '归属部门',
                        key: 'dept',
                        width: 90,
                        fixed: 'left',
                        filter: {
                            type: 'Input'
                        }
                    },
                    {
                        title: '岗位',
                        key: 'title',
                        width: 100,
                        fixed: 'left',
                        render: (h, params) => {

                            if(sf.isPfmAdmin || sf.isViewLeader || (sf.editKey != 'title' || sf.editRow != params.row._index)){
                                return h('div',{
                                    class:'t-edit-warp',
                                    on:{
                                        click:function(){
                                            sf.editKey =  'title';
                                            sf.editRow = params.row._index;
                                        }
                                    }
                                },[
                                    h('span',params.row.title ? params.row.title.name : ''),
                                    h(sf.isPfmAdmin || sf.isViewLeader ? null : 'i',{
                                        class:'iconfont icon-edit',
                                    })
                                ]);
                            }else{
                                return sf.renderTable('Select', params.row, h, 'title', 'prjPerformance','obj','cid')
                            }
                        },
                        filter: {
                            type: 'Select'
                        }
                    },
                    {
                        title: '归属组',
                        key: 'group',
                        width: 100,
                        render: (h, params) => {
                            if(sf.isPfmAdmin || sf.isViewLeader  || sf.editKey != 'group' || sf.editRow != params.row._index){
                                return h('div',{
                                    class:'t-edit-warp',
                                    on:{
                                        click:function(){
                                            sf.editKey =  'group';
                                            sf.editRow = params.row._index;
                                            sf.setFocus();
                                        }
                                    }
                                },[
                                    h('span',params.row.group),
                                    h(sf.isPfmAdmin || sf.isViewLeader ? null : 'i',{
                                        class:'iconfont icon-edit',
                                    })
                                ]);
                            }else{
                                return sf.renderTable('Input', params.row, h, 'group', 'prjPerformance',null)
                            }
                        },
                        filter: {
                            type: 'Input'
                        }
                    },
                    {
                        title: '打分人',
                        key: 'checkPsn',
                        width: 120,
                        render: (h, params) => {
                            if(sf.isPfmAdmin || sf.isViewLeader || (sf.editKey != 'checkPsn' || sf.editRow != params.row._index)){
                                var emp = params.row.checkPsn;
                                var userName = '';
                                var loginName = '';
                                if(!!emp){
                                    userName = emp.userName;
                                    loginName = emp.loginName;
                                }

                                return h('div',{
                                    class:'t-edit-warp',
                                    on:{
                                        click:function(){
                                            sf.editKey =  'checkPsn';
                                            sf.editRow = params.row._index;
                                        }
                                    }
                                },[
                                    h('span', userName+'/'+loginName),
                                    h(sf.isPfmAdmin || sf.isViewLeader ? null : 'i',{
                                        class:'iconfont icon-edit',
                                    })
                                ]);


                            }else{
                                return sf.renderTable('Select', params.row, h, 'checkPsn', 'prjPerformance','obj','userId')
                            }
                        },
                    },
                    {
                        title: '工时时长(天)',
                        key: 'mdStd',
                        width: 60,
                    },
                    {
                        title: '增补工时(天)',
                        key: 'mdAdjust',
                        width: 100,
                        renderHeader: function(h, params){
                            return h('div', [
                                h('Tooltip', {
                                    props: {
                                        placement: 'top',
                                        transfer: true,
                                        theme: 'dark',
                                        'max-width': 450
                                    }
                                }, [
                                    '增补工时(天)',
                                    h('span', {
                                        class: 'iconfont icon-qbzuocesvg02',
                                        style: {
                                            top: '-1px',
                                            right: '-18px',
                                            'margin-left': '5px',
                                            color: '#3883e5'
                                        }
                                    }),
                                    h('div', {
                                            slot: 'content',
                                            style: {
                                                whiteSpace: 'normal',
                                                fontSize: '12px'
                                            }
                                        }, '仅少量临时支撑人员，及不填写工时人员可以进行增补工时，并需要对增补工时进行说明'
                                    )])])
                        },
                        render: (h, params) => {
                            if(sf.isPfmAdmin || sf.isViewLeader || (sf.editKey != 'mdAdjust' || sf.editRow != params.row._index)){
                                return h('div',{
                                    class:'t-edit-warp',
                                    on:{
                                        click:function(){
                                            sf.editKey =  'mdAdjust';
                                            sf.editRow = params.row._index;
                                            sf.setFocus();
                                        }
                                    }
                                },[
                                    h('span', params.row.mdAdjust),
                                    h(sf.isPfmAdmin || sf.isViewLeader ? null : 'i',{
                                        class:'iconfont icon-edit'
                                    })
                                ]);
                            }else{
                                return sf.renderTable('InputNumber', params.row, h, 'mdAdjust', 'prjPerformance',null)
                            }
                        },
                    },
                    {
                        title: '增补说明',
                        key: 'mdAdjustDesc',
                        width: 150,
                        render: (h, params) => {
                            if(sf.isPfmAdmin || sf.isViewLeader || (sf.editKey != 'mdAdjustDesc' || sf.editRow != params.row._index)){
                                return h('div',{
                                    class:'t-edit-warp',
                                    on:{
                                        click:function(){
                                            sf.editKey =  'mdAdjustDesc';
                                            sf.editRow = params.row._index;
                                            sf.setFocus();
                                        }
                                    }
                                },[
                                    h('span', params.row.mdAdjustDesc),
                                    h(sf.isPfmAdmin || sf.isViewLeader ? null : 'i',{
                                        class:'iconfont icon-edit',
                                    })
                                ]);
                            }else{
                                return sf.renderTable('Input', params.row, h, 'mdAdjustDesc', 'prjPerformance',null)
                            }
                        },
                    },
                ],
                prjPerformanceData:[],
                prjPerformanceData1:[],
                selectDataMap: {
                    title: [],
                    employeeType: [
                        {
                            value: true,
                            label: '正式',
                        },
                        {
                            value: false,
                            label: '外包',
                        },
                    ],
                    isPfmEmp: [
                        {
                            value: true,
                            label: '是',
                        },
                        {
                            value: false,
                            label: '否',
                        },
                    ]
                },



                prjPerformanceColumns1:[
                    {
                        title: '序号',
                        type: 'index',
                        width: 40,
                        fixed: 'left'
                    },
                    {
                        title: '人员',
                        key: 'emp',
                        width: 100,
                        fixed: 'left',
                        filter: {
                            type: 'Input'
                        },
                        render: (h, params) => {
                            var emp = params.row.emp;
                            var userName = '';
                            var loginName = '';
                            if(!!emp){
                                userName = emp.userName;
                                loginName = emp.loginName;
                                return h('span', userName+'/'+loginName);
                            }
                        }
                    },
                    {
                        title: '人员类型',
                        key: 'employeeType',
                        width: 80,
                        fixed: 'left',
                        filter: {
                            type: 'Select'
                        }
                    },
                    {
                        title: '归属部门',
                        key: 'dept',
                        width: 90,
                        fixed: 'left',
                        filter: {
                            type: 'Input'
                        }
                    },
                    {
                        title: '岗位',
                        key: 'title',
                        width: 100,
                        fixed: 'left',
                        filter: {
                            type: 'Select'
                        },
                        render: (h, params) => {
                            return h('span', params.row.title.name);
                        },
                    },
                    {
                        title: '归属组',
                        key: 'group',
                        width: 100,
                        filter: {
                            type: 'Input'
                        },
                    },
                    {
                        title: '工作绩效(80%)',
                        align: 'center',
                        renderHeader: function(h, params){
                            return h('div', [
                                h('Tooltip', {
                                    props: {
                                        placement: 'top',
                                        transfer: true,
                                        theme: 'dark',
                                        'max-width': 450
                                    }
                                }, [
                                    '工作绩效(80%)',
                                    h('span', {
                                        class: 'iconfont icon-qbzuocesvg02',
                                        style: {
                                            top: '-1px',
                                            right: '-18px',
                                            'margin-left': '5px',
                                            color: '#3883e5'
                                        }
                                    }),
                                    h('div', {
                                            slot: 'content',
                                            style: {
                                                whiteSpace: 'normal',
                                                fontSize: '12px'
                                            }
                                        }, '工作量、工作质量，按百分制打分，各子项满分100分'
                                    )])])
                        },
                        children: [
                            { title: '工作量(50%)', key: 'workEffortScore', width: 100,
                                render: (h, params) => {
                                    if(!params.row.workEffortScore){
                                        params.row.workEffortScore = {
                                            score: 0,
                                        }
                                    }
                                    return h('span', params.row.workEffortScore.score);
                                },
                            },
                            { title: '工作质量(50%)', key: 'workQualityScore', width: 100,
                                render: (h, params) => {
                                    if(!params.row.workQualityScore){
                                        params.row.workQualityScore = {
                                            score: 0,
                                        }
                                    }
                                    return h('span', params.row.workQualityScore.score);
                                },
                            }
                        ]
                    },
                    {
                        title: '工作态度(20%)',
                        align: 'center',
                        renderHeader: function(h, params){
                            return h('div', [
                                h('Tooltip', {
                                    props: {
                                        placement: 'top',
                                        transfer: true,
                                        theme: 'dark',
                                        'max-width': 450
                                    }
                                }, [
                                    '工作态度(20%)',
                                    h('span', {
                                        class: 'iconfont icon-qbzuocesvg02',
                                        style: {
                                            top: '-1px',
                                            right: '-18px',
                                            'margin-left': '5px',
                                            color: '#3883e5'
                                        }
                                    }),
                                    h('div', {
                                            slot: 'content',
                                            style: {
                                                whiteSpace: 'normal',
                                                fontSize: '12px'
                                            }
                                        }, '积极性、沟通能力、吃苦耐劳，按百分制打分，各子项目满分100分'
                                    )])])
                        },
                        children: [
                            { title: '积极性(50%)', key: 'initiativeScore', width: 100,
                                render: (h, params) => {
                                    if(sf.isPfmAdmin || sf.isViewLeader || ((sf.editKey != params.column.key || sf.editRow != params.row._index)  && sf.pfmType != 'submit')){
                                        return h('div',{
                                            class:'t-edit-warp',
                                            on:{
                                                click:function(){
                                                    sf.editKey =  'initiativeScore';
                                                    sf.editRow = params.row._index;
                                                    sf.setFocus();
                                                }
                                            }
                                        },[
                                            h('span', params.row.initiativeScore),
                                            h(sf.isPfmAdmin || sf.isViewLeader ? null : 'i',{
                                                class:'iconfont icon-edit',

                                            })
                                        ]);
                                        return h('span', params.row.initiativeScore);
                                    }else{
                                        return sf.renderTable('InputNumber', params.row, h, 'initiativeScore', 'prjPerformance',null)
                                    }
                                },
                            },
                            { title: '沟通能力(20%)', key: 'comSkillScore', width: 100,
                                render: (h, params) => {
                                    if(sf.isPfmAdmin || sf.isViewLeader || ((sf.editKey != params.column.key || sf.editRow != params.row._index)  && sf.pfmType != 'submit')){
                                        return h('div',{
                                            class:'t-edit-warp',
                                            on:{
                                                click:function(){
                                                    sf.editKey =  'comSkillScore';
                                                    sf.editRow = params.row._index;
                                                    sf.setFocus();
                                                }
                                            }
                                        },[
                                            h('span', params.row.comSkillScore),
                                            h(sf.isPfmAdmin || sf.isViewLeader ? null : 'i',{
                                                class:'iconfont icon-edit',
                                            })
                                        ]);
                                    }else{
                                        return sf.renderTable('InputNumber', params.row, h, 'comSkillScore', 'prjPerformance',null)
                                    }
                                },
                            },
                            { title: '吃苦耐劳(30%)', key: 'hardWorkScore', width: 100,
                                render: (h, params) => {
                                    if(sf.isPfmAdmin || sf.isViewLeader || ((sf.editKey != params.column.key || sf.editRow != params.row._index)  && sf.pfmType != 'submit')){
                                        return h('div',{
                                            class:'t-edit-warp',
                                            on:{
                                                click:function(){
                                                    sf.editKey =  'hardWorkScore';
                                                    sf.editRow = params.row._index;
                                                    sf.setFocus();
                                                }
                                            }
                                        },[
                                            h('span', params.row.hardWorkScore),
                                            h(sf.isPfmAdmin || sf.isViewLeader ? null : 'i',{
                                                class:'iconfont icon-edit',
                                            })
                                        ]);
                                    }else{
                                        return sf.renderTable('InputNumber', params.row, h, 'hardWorkScore', 'prjPerformance',null)
                                    }
                                },
                            }
                        ]
                    },
                    {
                        title: '打分说明',
                        key: 'scoreDesc',
                        width: 150,
                        render: (h, params) => {
                            if(sf.isPfmAdmin || sf.isViewLeader || ((sf.editKey != params.column.key || sf.editRow != params.row._index)  && sf.pfmType != 'submit')){
                                return h('div',{
                                    class:'t-edit-warp',
                                    on:{
                                        click:function(){
                                            sf.editKey =  'scoreDesc';
                                            sf.editRow = params.row._index;
                                            sf.setFocus();
                                        }
                                    }
                                },[
                                    h('span', params.row.scoreDesc),
                                    h(sf.isPfmAdmin || sf.isViewLeader ? null : 'i',{
                                        class:'iconfont icon-edit',
                                    })
                                ]);
                            }else{
                                return sf.renderTable('Input', params.row, h, 'scoreDesc', 'prjPerformance',null)
                            }
                        },
                    },
                    { title: '基础得分', key: 'baseScore', width: 100},
                ],

                prjPerformanceColumns2: [
                    { type: 'selection', width: 40, fixed: 'left' },
                    {
                        title: '序',
                        type:'index',
                        width: 40,
                        fixed: 'left'
                    },
                    { title: '是否参与激励',
                        key: 'isPfmEmp',
                        width: 70,
                        fixed: 'left',
                        render: (h, params) => {
                            if(sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader){
                                return h('span', params.row.isPfmEmp ? '是' : '否');
                            }else{
                                return sf.renderTable('i-switch', params.row, h, 'isPfmEmp', 'prjPerformance','obj')
                            }
                        },
                        filter: {
                            type: 'Select'
                        }
                    },
                    {
                        title: '人员',
                        key: 'emp',
                        width: 100,
                        fixed: 'left',
                        render: (h, params) => {
                            var emp = params.row.emp;
                            var userName = '';
                            var loginName = '';
                            if(!!emp){
                                userName = emp.userName;
                                loginName = emp.loginName;
                                return h('span', userName+'/'+loginName);
                            }
                        },
                        filter: {
                            type: 'Input'
                        }
                    },
                    {
                        title: '人员类型',
                        key: 'employeeType',
                        width: 80,
                        fixed: 'left',
                        filter: {
                            type: 'Select'
                        }
                    },
                    {
                        title: '归属部门',
                        key: 'dept',
                        width: 90,
                        fixed: 'left',
                        filter: {
                            type: 'Input'
                        }
                    },
                    {
                        title: '岗位',
                        key: 'title',
                        width: 100,
                        fixed: 'left',
                        render: (h, params) => {
                            if((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader) || ((sf.editKey != params.column.key || sf.editRow != params.row._index))){
                                return h('div',{
                                    class:'t-edit-warp',
                                    on:{
                                        click:function(){
                                            sf.editKey =  'title';
                                            sf.editRow = params.row._index;
                                        }
                                    }
                                },[
                                    h('span',params.row.title ? params.row.title.name : ''),
                                    h((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader)  ? null : 'i',{
                                        class:'iconfont icon-edit',
                                    })
                                ]);
                            }else{
                                return sf.renderTable('Select', params.row, h, 'title', 'prjPerformance','obj','cid')
                            }

                        },
                        filter: {
                            type: 'Select'
                        }
                    },
                    {
                        title: '岗位系数',
                        key: 'titleRatio',
                        width: 80,
                    },
                    {
                        title: '归属组',
                        key: 'group',
                        width: 100,
                        render: (h, params) => {
                            if((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader) || ((sf.editKey != params.column.key || sf.editRow != params.row._index))){
                                return h('div',{
                                    class:'t-edit-warp',
                                    on:{
                                        click:function(){
                                            sf.editKey =  'group';
                                            sf.editRow = params.row._index;
                                            sf.setFocus();
                                        }
                                    }
                                },[
                                    h('span',params.row.group),
                                    h((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader)  ? null : 'i',{
                                        class:'iconfont icon-edit',
                                    })
                                ]);
                            }else{
                                return sf.renderTable('Input', params.row, h, 'group', 'prjPerformance',null)
                            }
                        },
                        filter: {
                            type: 'Input'
                        }
                    },
                    {
                        title: '打分人',
                        key: 'checkPsn',
                        width: 120,
                        render: (h, params) => {
                            if((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader) || ((sf.editKey != params.column.key || sf.editRow != params.row._index))){
                                var emp = params.row.checkPsn;
                                var userName = '';
                                var loginName = '';
                                if(!!emp){
                                    userName = emp.userName;
                                    loginName = emp.loginName;
                                }
                                return h('div',{
                                    class:'t-edit-warp',
                                    on:{
                                        click:function(){
                                            sf.editKey =  'checkPsn';
                                            sf.editRow = params.row._index;

                                        }
                                    }
                                },[
                                    h('span', userName+'/'+loginName),
                                    h((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader)  ? null : 'i',{
                                        class:'iconfont icon-edit',
                                    })
                                ]);
                            }else{
                                return sf.renderTable('Select', params.row, h, 'checkPsn', 'prjPerformance','obj','userId')
                            }
                        },
                    },
                    // 多级表头部分
                    {
                        title: '工作绩效(80%)',
                        align: 'center',
                        renderHeader: function(h, params){
                            return h('div', [
                                h('Tooltip', {
                                    props: {
                                        placement: 'top',
                                        transfer: true,
                                        theme: 'dark',
                                        'max-width': 450
                                    }
                                }, [
                                    '工作绩效(80%)',
                                    h('span', {
                                        class: 'iconfont icon-qbzuocesvg02',
                                        style: {
                                            top: '-1px',
                                            right: '-18px',
                                            'margin-left': '5px',
                                            color: '#3883e5'
                                        }
                                    }),
                                    h('div', {
                                            slot: 'content',
                                            style: {
                                                whiteSpace: 'normal',
                                                fontSize: '12px'
                                            }
                                        }, '工作量、工作质量，按百分制打分，各子项满分100分'
                                    )])])
                        },
                        children: [
                            { title: '工作量(50%)', key: 'workEffortScore', width: 100,
                                render: (h, params) => {
                                    if(!params.row.workEffortScore){
                                        params.row.workEffortScore = {
                                            score: 0,
                                        }
                                    }

                                    if((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader) || ((sf.editKey != params.column.key || sf.editRow != params.row._index))){
                                        return h('div',{
                                            class:'t-edit-warp',
                                            on:{
                                                click:function(){
                                                    sf.editKey =  'workEffortScore';
                                                    sf.editRow = params.row._index;
                                                    sf.setFocus();
                                                }
                                            }
                                        },[
                                            h('span',params.row.workEffortScore.score),
                                            h((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader)  ? null : 'i',{
                                                class:'iconfont icon-edit',
                                            })
                                        ]);
                                    }else{
                                        return sf.renderTable('InputNumber', params.row, h, 'workEffortScore', 'prjPerformance',null)
                                    }
                                },
                            },
                            { title: '工作质量(50%)', key: 'workQualityScore', width: 100,
                                render: (h, params) => {
                                    if(!params.row.workQualityScore){
                                        params.row.workQualityScore = {
                                            score: 0,
                                        }
                                    }

                                    if((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader) || ((sf.editKey != params.column.key || sf.editRow != params.row._index))){
                                        return h('div',{
                                            class:'t-edit-warp',
                                            on:{
                                                click:function(){
                                                    sf.editKey =  'workQualityScore';
                                                    sf.editRow = params.row._index;
                                                    sf.setFocus();
                                                }
                                            }
                                        },[
                                            h('span',params.row.workQualityScore.score),
                                            h((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader)  ? null : 'i',{
                                                class:'iconfont icon-edit',
                                            })
                                        ]);
                                    }else{
                                        return sf.renderTable('InputNumber', params.row, h, 'workQualityScore', 'prjPerformance',null)
                                    }
                                },
                            }
                        ]
                    },
                    {
                        title: '工作态度(20%)',
                        align: 'center',
                        renderHeader: function(h, params){
                            return h('div', [
                                h('Tooltip', {
                                    props: {
                                        placement: 'top',
                                        transfer: true,
                                        theme: 'dark',
                                        'max-width': 450
                                    }
                                }, [
                                    '工作态度(20%)',
                                    h('span', {
                                        class: 'iconfont icon-qbzuocesvg02',
                                        style: {
                                            top: '-1px',
                                            right: '-18px',
                                            'margin-left': '5px',
                                            color: '#3883e5'
                                        }
                                    }),
                                    h('div', {
                                            slot: 'content',
                                            style: {
                                                whiteSpace: 'normal',
                                                fontSize: '12px'
                                            }
                                        }, '积极性、沟通能力、吃苦耐劳，按百分制打分，各子项目满分100分'
                                    )])])
                        },
                        children: [
                            { title: '积极性(50%)', key: 'initiativeScore', width: 100,
                                render: (h, params) => {
                                    if((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader) || ((sf.editKey != params.column.key || sf.editRow != params.row._index))){
                                        return h('div',{
                                            class:'t-edit-warp',
                                            on:{
                                                click:function(){
                                                    sf.editKey =  'initiativeScore';
                                                    sf.editRow = params.row._index;
                                                    sf.setFocus();
                                                }
                                            }
                                        },[
                                            h('span',params.row.initiativeScore),
                                            h((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader)  ? null : 'i',{
                                                class:'iconfont icon-edit',
                                            })
                                        ]);
                                    }else{
                                        return sf.renderTable('InputNumber', params.row, h, 'initiativeScore', 'prjPerformance',null)
                                    }
                                },
                            },
                            { title: '沟通能力(20%)', key: 'comSkillScore', width: 100,
                                render: (h, params) => {
                                    if((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader) || ((sf.editKey != params.column.key || sf.editRow != params.row._index))){
                                        return h('div',{
                                            class:'t-edit-warp',
                                            on:{
                                                click:function(){
                                                    sf.editKey =  'comSkillScore';
                                                    sf.editRow = params.row._index;
                                                    sf.setFocus();
                                                }
                                            }
                                        },[
                                            h('span',params.row.comSkillScore),
                                            h((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader)  ? null : 'i',{
                                                class:'iconfont icon-edit',
                                            })
                                        ]);
                                    }else{
                                        return sf.renderTable('InputNumber', params.row, h, 'comSkillScore', 'prjPerformance',null)
                                    }
                                },
                            },
                            { title: '吃苦耐劳(30%)', key: 'hardWorkScore', width: 100,
                                render: (h, params) => {
                                    if((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader) || ((sf.editKey != params.column.key || sf.editRow != params.row._index))){
                                        return h('div',{
                                            class:'t-edit-warp',
                                            on:{
                                                click:function(){
                                                    sf.editKey =  'hardWorkScore';
                                                    sf.editRow = params.row._index;
                                                    sf.setFocus();
                                                }
                                            }
                                        },[
                                            h('span',params.row.hardWorkScore),
                                            h((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader)  ? null : 'i',{
                                                class:'iconfont icon-edit',
                                            })
                                        ]);
                                    }else{
                                        return sf.renderTable('InputNumber', params.row, h, 'hardWorkScore', 'prjPerformance',null)
                                    }
                                },
                            }
                        ]
                    },
                    {
                        title: '打分说明',
                        key: 'scoreDesc',
                        width: 150,
                        render: (h, params) => {
                            if((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader) || ((sf.editKey != params.column.key || sf.editRow != params.row._index))){
                                return h('div',{
                                    class:'t-edit-warp',
                                    on:{
                                        click:function(){
                                            sf.editKey =  'scoreDesc';
                                            sf.editRow = params.row._index;
                                            sf.setFocus();
                                        }
                                    }
                                },[
                                    h('span', params.row.scoreDesc),
                                    h((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader)  ? null : 'i',{
                                        class:'iconfont icon-edit',
                                    })
                                ]);
                            }else{
                                return sf.renderTable('Input', params.row, h, 'scoreDesc', 'prjPerformance',null)
                            }
                        },
                    },
                    { title: '基础得分', key: 'baseScore', width: 100},
                    { title: '工时时长(天)', key: 'mdStd', width: 60 },
                    { title: '增补工时(天)', key: 'mdAdjust', width: 100,
                        renderHeader: function(h, params){
                            return h('div', [
                                h('Tooltip', {
                                    props: {
                                        placement: 'top',
                                        transfer: true,
                                        theme: 'dark',
                                        'max-width': 450
                                    }
                                }, [
                                    '增补工时(天)',
                                    h('span', {
                                        class: 'iconfont icon-qbzuocesvg02',
                                        style: {
                                            top: '-1px',
                                            right: '-18px',
                                            'margin-left': '5px',
                                            color: '#3883e5'
                                        }
                                    }),
                                    h('div', {
                                            slot: 'content',
                                            style: {
                                                whiteSpace: 'normal',
                                                fontSize: '12px',
                                            }
                                        }, '仅少量临时支撑人员，及不填写工时人员可以进行增补工时，并需要对增补工时进行说明'
                                    )])])
                        },
                        render: (h, params) => {
                            if((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader) || ((sf.editKey != params.column.key || sf.editRow != params.row._index))){
                                return h('div',{
                                    class:'t-edit-warp',
                                    on:{
                                        click:function(){
                                            sf.editKey =  'mdAdjust';
                                            sf.editRow = params.row._index;
                                            sf.setFocus();
                                        }
                                    }
                                },[
                                    h('span',params.row.mdAdjust),
                                    h((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader)  ? null : 'i',{
                                        class:'iconfont icon-edit',
                                    })
                                ]);
                            }else{
                                return sf.renderTable('InputNumber', params.row, h, 'mdAdjust', 'prjPerformance',null)
                            }
                        },
                    },
                    { title: '增补说明', key: 'mdAdjustDesc', width: 150,
                        render: (h, params) => {
                            if((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader) || ((sf.editKey != params.column.key || sf.editRow != params.row._index))){
                                return h('div',{
                                    class:'t-edit-warp',
                                    on:{
                                        click:function(){
                                            sf.editKey =  'mdAdjustDesc';
                                            sf.editRow = params.row._index;
                                            sf.setFocus();
                                        }
                                    }
                                },[
                                    h('span',params.row.mdAdjustDesc),
                                    h((sf.pfmType === 'approve' || sf.isPfmAdmin || sf.isViewLeader)  ? null : 'i',{
                                        class:'iconfont icon-edit',
                                    })
                                ]);
                            }else{
                                return sf.renderTable('Input', params.row, h, 'mdAdjustDesc', 'prjPerformance',null)
                            }
                        },
                    },
                    { title: '项目时长系数', key: 'mdRatio', width: 120},
                    { title: '累计差旅天数', key: 'tripDays', width: 120 },
                    { title: '累计差旅次数', key: 'tripTimes', width: 120 },
                    { title: '平均差旅时长系数', key: 'tripRatio', width: 150,
                        render: (h, params) => {
                            var row = params.row;
                            var tripRatio = row.tripRatio === null || row.tripRatio === undefined ? 1 : row.tripRatio;
                            row.tripRatio = tripRatio;
                            return h('span', tripRatio);
                        },
                    },
                    { title: '个人绩效分', key: 'empPfmRatio', width: 120},
                    { title: '项目管理系数', key: 'prjMgtRatio', width: 120 }
                ],

                editKey:'',
                editRow:'',
                prjPerformanceLoading:true,

                prjBasicInfo:{
                    name:'',
                    level:'',
                    province:'',
                    incentivePeriod:'',
                    assessmentScore:'',
                    planManMonth:'',
                    actualManMonth:'',
                    noIncentiveManMonth:'',
                    bonusActual:0,
                },


                planBonusColumns:[],

                actualBonusColumns:[],

                bonusAnalysisColumns:[],

                distributionColumns:[],
                distributionColumns1:[],
                distributionColumns2:[],

                planBonusData:[],

                actualBonusData:[],

                bonusAnalysisData:[],

                distributionData1:[],

                distributionData2:[],

                pfmType:null, // start: 发起绩效评分, submit: 提交绩效评分
                isPfmAdmin:false,
                pfmAdminList:[],
                buPrjLeader:[],
                pfmApply:{},

                tabaleParam:{
                    title:'',
                    emp:'',
                    group:'',
                    dept:'',
                },

                isAddUserLoading:false,

                approveReject:'',
                approvePass:'',
                initPrjLoading:false,
                bonusActual:0,

                isShowResult:false,

                vpnPwd:null,
                viewMoneyModal:false,
                isExport:false,
                isEncryption:true,

                exportIndex:1,

                showDrawer:false,

                selectedRows:[],

                descTitle: 'detailsDesc',
                isExpand: false,
            }
        },
        watch:{
            'prjId': function(newVal, oldVal) {
                var sf = this;
                if(newVal){
                    sf.listPrjEmpPfmApplyByPrjId(newVal);
                }
            },
            'currentUser': function(newVal, oldVal) {
                var sf = this;
                if(newVal){
                    sf.isPfmAdmin = sf.checkPfmAdmin();
                    sf.getSystemServerTime();
                }
            },

            showDrawer:function(newVal,oldVal){
                var sf = this;
                if(newVal){
                    document.body.classList.add('showDrawer');
                }else{
                    document.body.classList.remove('showDrawer');
                }
            },
            prjPerformanceData1:function(newVal,oldVal){
                var sf = this;
                if(newVal.length > 10){
                    sf.tableHeight = window.innerHeight - 210;
                }else{
                    sf.tableHeight = null;
                }
            }
        },
        created: function(){
            var sf = this;
            sf.tabHeight = document.documentElement.clientHeight - 156  + 'px';
            sf.getOpts();
            sf.prjId = sf.getUrlQueryString("prjId");

            sf.loadInitPrjInfo(sf.prjId);
            sf.findUserByBu();
            sf.getPfmAdmin();
        },
        mounted: function(){
            var sf = this;

            sf.descTableHeight = document.documentElement.clientHeight - 140;

            // 获取指定元
            const scrollview = this.$refs.scrollview
            // 添加滚动监听，该滚动监听了拖拽滚动条
            // 尾部的 true 最好加上，我这边测试没加 true ，拖拽滚动条无法监听到滚动，加上则可以监听到拖拽滚动条滚动回调
            scrollview.addEventListener('scroll', this.handleScroll, true)

            Vue.evtHub.$on("prj-choosed", function (data) {
                console.log('prj-choosed',data);
                sf.prjId = data.prjId;
                sf.prjName = data.prjName;
                sf.prjCode = data.prjCode;
                sf.prjIdValue = data.prjIdValue;
                sf.selectPrjSbuId = data.sbuId;
                sf.loadInitPrjInfo(sf.prjId);
                location.reload();
            });

            Vue.evtHub.$on('sort-change',function (data) {
                console.log(data);
                var key = data.column.key;
                var order = data.column.order
                sf.prjPerformanceData1.sort(function(a, b){
                    return order === "asc" ?  a[key] - b[key] : b[key] - a[key]
                });
            });


            Vue.evtHub.$on("select-data", function(data) {
                sf.selectedRows = data.selection || [];
            });

            // 列表头筛选
            Vue.evtHub.$on("load-data", function(data) {
                console.log('load-data',data);
                if(data.fieldId === 'title'){
                    var item = sf.selectDataMap.title.find(item => item.value === data.value);

                    sf.tabaleParam.title = !!item ? item.label : null;
                }else{
                    sf.tabaleParam[data.fieldId] = data.value;
                }
                sf.getPrjBonusList();
            });
        },
        methods: {


            setWaterMark(){
                var sf = this;
                // 获取元素prj_b_l_bot，并生成canvas图片
                var prjBLBot = document.getElementById('prj_b_l_bot');
                if (prjBLBot) {
                    // 使用html2canvas库将元素渲染为canvas
                    html2canvas(prjBLBot).then(function(canvas) {
                        // 将canvas添加到body或者你需要的地方
                        const ctx = canvas.getContext('2d');

                        // 将canvas转换为图片并插入到页面
                        var img = new Image();
                        img.src = canvas.toDataURL("image/png");
                        img.style.maxWidth = "100%";
                        img.style.display = "block";
                        // 可根据需要插入到指定位置，这里插入到prj_b_l_bot元素后面
                        prjBLBot.parentNode.insertBefore(img, prjBLBot.nextSibling);
                        return;


                        let textData;
                        ctx.font = '15px Microsoft Yahei';
                        ctx.fillStyle = 'red'
                        ctx.fillText('bruce好帅我好爱', 60, 130);
                        textData = ctx.getImageData(0, 0, ctx.canvas.width, ctx.canvas.height).data;
                        sf.mergeData(textData, 'R')

                    }).catch(function(error) {
                        console.error('生成canvas图片失败:', error);
                    });
                } else {
                    console.warn('未找到id为prj_b_l_bot的元素');
                }
            },


            mergeData(newData, color) {
                // 获取图片的data
                var oData = originData.data
                console.log(newData, originData)
                // bit是要改的通道所在的位置， offset是alpha相对于通道所在的位置
                var bit, offset

                // 判断是修改哪个颜色通道
                switch(color) {
                    case 'R':
                        bit = 0
                        offset = 3
                        break;
                    case 'G':
                        bit = 1
                        offset = 2
                        break;
                    case 'B':
                        bit = 2
                        offset = 1
                        break;
                }

                // r g b a
                for (var i = 0; i < oData.length; i++) {
                    if (i % 4 === bit) {
                        // 只修改目标通道
                        if (newData[i + offset] === 0 && (oData[i] % 2 === 1)) {
                            // 没有信息的像素，将目标通道的奇数像素改为偶数
                            if (oData[i] === 255) {
                                oData[i]--
                            } else {
                                oData[i]++
                            }
                        } else if (newData[i + offset] !== 0 && (oData[i] % 2 === 0)) {
                            // 有信息的像素
                            oData[i]++
                        }
                    }
                }
                ctx.putImageData(originData, 0, 0)
            },


            //获取URL参数
            getUrlQueryString:function(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]);
                return null;
            },


            getSystemServerTime() {
                var sf = this;
                Ajax.send({
                    url: '/linkus-rms/rmsMdCtrl/getSystemServerTime',
                    method: 'GET',
                    data: {}
                }).then(function(res) {
                    res = res || [];
                    if (JSON.stringify(res) === '[]') return;
                    //水印
                    var watermarkTxt = sf.currentUser.userName+'/'+sf.currentUser.loginName+'/'+res.data;

                    (res.data!=null) ? watermark({'watermark_txt':watermarkTxt,watermark_width:300,watermark_height:160,watermark_x:10,watermark_y:100},true,'watermarkWarp',false) : null;

                }).catch(function(error) {
                    // 可以根据需要处理错误
                    console.error('获取系统服务器时间失败', error);
                    return null;
                });
            },


            getHasAuth(){
                var sf = this;
                Ajax.send({
                    url: '/linkus-passport/radius/hasAuth',
                    method: 'GET',
                }).then(function(res) {
                    if(res.data){
                        sf.exportPrjBonus();
                    }else{
                        sf.viewMoneyModal = true;
                        sf.isExport = true;
                    }
                }).catch(function(error) {
                    sf.$Message.error('获取项目周期开始月失败');
                });

            },

            handleScroll(){
                var sf = this;
            },

            getstartMonth(){
                var sf = this;
                Ajax.send({
                    url: '/linkus-prj/prjDepartWorkingHoursCtrl/getYearMonthList.action',
                    method: 'GET',
                    data: {
                        prjId: sf.prjId
                    }
                }).then(function(res) {
                    if(!!res && res.length > 0){
                        sf.initPrjData.startMonth = res[0];
                    }
                }).catch(function(error) {
                    sf.$Message.error('获取项目周期开始月失败');
                });
            },

            checkPfmAdmin(){
                var sf = this;
                return sf.pfmAdminList.some(item => item.id === sf.currentUser.id);
            },

            //获取考评管理员
            getPfmAdmin(){
                var sf = this;
                Ajax.send({
                    url: '/linkus-prj/prjRoleCtrl/queryRoleUserForBU.action',
                    method: 'GET',
                    data: {
                        defId: '5a70111329974f7cabe9115d'
                    }
                }).then(function(res) {
                    console.log(res);
                    if(res.success){
                        sf.pfmAdminList = res.data.prjEvalAdmin || [];
                        sf.buPrjLeader = res.data.buPrjLeader || [];
                        sf.prjOprtDir = res.data.prjOprtDir || [];
                        sf.isPfmAdmin = sf.checkPfmAdmin();
                        sf.isViewLeader = sf.buPrjLeader.some(item => item.id === sf.currentUser.id) || sf.prjOprtDir.some(item => item.id === sf.currentUser.id)
                    }else{
                        sf.$Message.error(res.message || '获取考评管理员失败');
                    }
                }).catch(function(error) {
                    sf.$Message.error('获取考评管理员失败');
                });
            },


            updateModal(){
                var sf = this;
                sf.initPrjData.startMonth = sf.pfmApply.startYm;
                sf.initPrjData.endMonth = sf.pfmApply.endYm;
                sf.initPrjData.projectLocation = sf.pfmApply.site ? sf.pfmApply.site.split(',') : [];
                sf.initPrjModal = true;
                sf.updatePrj = true;
            },

            // 获取项目绩效申请列表
            listPrjEmpPfmApplyByPrjId(prjId,type) {
                var sf = this;
                sf.prjEmpPfmApplyLoading = true;
                Ajax.send({
                    url: '/linkus-prj/prjBonusCtrl/listPrjEmpPfmApplyByPrjId.action',
                    method: 'POST',
                    data: {
                        prjId: prjId
                    }
                }).then(function(res) {
                    sf.prjEmpPfmApplyLoading = false;
                    if(res.success){
                        sf.prjEmpPfmApplyList = res.data || [];
                        if(sf.prjEmpPfmApplyList.length > 0){
                            var pfmApply = sf.prjEmpPfmApplyList[0];
                            sf.pfmApply = pfmApply;
                            if(pfmApply.bonusActual && pfmApply.bonusActual.indexOf('*') > -1){
                                sf.isEncryption = true;
                            }else{
                                sf.isEncryption = false;
                            }
                            sf.pfmApplyId = pfmApply.id;
                            sf.prjBasicInfo.incentivePeriod = pfmApply.startYm + ' - ' + pfmApply.endYm;
                            if(type != 'reset'){
                                sf.getApprStatusFlow();
                                sf.getApplyHist(pfmApply.id);
                            }else{
                                sf.getPrjBonusList();
                            }
                        }else{
                            sf.getstartMonth();
                        }
                    }else{
                        sf.$Message.error(res.message);
                    }
                }).catch(function(error) {
                    sf.$Message.error('获取项目绩效申请列表失败');
                });
            },

            getApprStatusFlow() {
                var sf = this;
                return Ajax.send({
                    url: '/linkus-sys-user/sysUserApplyCtrl/getApprStatusFlow.action',
                    method: 'POST',
                    data: {
                        cnfgTypeId: '68216447fb03e0e17312895c',
                        rcdId:sf.pfmApplyId
                    }
                }).then(function(res) {
                    if (res.success) {
                        // 处理返回的审批流数据
                        var apprStatusFlow = res.data || [];
                        var isPass = true;
                        apprStatusFlow.forEach(function (item,i){
                            item.nodeStatus = isPass ? 'pass' : '';
                            if(item.current){
                                item.nodeStatus = item.current ? 'current' : 'pass';
                                if(item.status.cid === '6821c393fb03e0e1732c0162'){
                                    sf.pfmType = 'start';
                                    sf.exportIndex = 1;
                                }else if(item.status.cid === '6821c3aafb03e0e1732c0706'){
                                    sf.pfmType = 'submit';
                                    if(sf.isPm || sf.isPfmAdmin || sf.isViewLeader){
                                        sf.prjPerformanceColumns = sf.prjPerformanceColumns2;
                                    }else{
                                        sf.prjPerformanceColumns = sf.prjPerformanceColumns1;
                                    }
                                    sf.exportIndex = 2;
                                }else if(item.status.cid === '6821c3defb03e0e1732c146b' || item.status.cid === '6821c418fb03e0e1732c2368'){
                                    sf.pfmType = 'approve';
                                    sf.approveReject = item.status.cid === '6821c3defb03e0e1732c146b' ? '驳回' : '审批不通过';
                                    sf.approvePass = item.status.cid === '6821c3defb03e0e1732c146b' ? '提交额度审批' : '审批通过';
                                    // 删除数组第一列
                                    if (Array.isArray(sf.prjPerformanceColumns2) && sf.prjPerformanceColumns2.length > 0) {
                                        sf.prjPerformanceColumns2.splice(0, 1);
                                    }

                                    var columns = sf.prjPerformanceColumns2.concat([]);
                                    var columns1 = [
                                        {
                                            title: '计划发放金额(元)',
                                            key: 'bonusDue',
                                            width: 120,
                                        },
                                    ];

                                    columns.splice.apply(columns, [8, 0].concat(columns1));

                                    sf.prjPerformanceColumns = columns;
                                    sf.exportIndex = 3;
                                }else if(item.status.cid === '6821c433fb03e0e1732c2a88'){
                                    sf.pfmType = 'pmApprove';

                                    var columns = sf.prjPerformanceColumns2.concat([]);
                                    sf.exportIndex = 4;
                                    var columns1 = [
                                        {
                                            title: '计划发放金额(元)',
                                            key: 'bonusDue',
                                            width: 130,
                                            sortable: true,
                                        },
                                        { title: '实际发放金额(元)', key: 'bonusActual', width: 130,
                                            sortable: true,
                                            render: (h, params) => {
                                                if(sf.isPfmAdmin || sf.isViewLeader || !params.row.isPfmEmp || sf.isEncryption || (sf.editKey != 'bonusActual' || sf.editRow != params.row._index)){
                                                    return h('div',{
                                                        class:'t-edit-warp',
                                                        on:{
                                                            click:function(){
                                                                sf.editKey =  'bonusActual';
                                                                sf.editRow = params.row._index;
                                                                sf.setFocus();
                                                            }
                                                        }
                                                    },[
                                                        h('span',params.row.bonusActual),
                                                        h((sf.isPfmAdmin || !params.row.isPfmEmp || sf.isEncryption || sf.isViewLeader)  ? null : 'i',{
                                                            class:'iconfont icon-edit',
                                                        })
                                                    ]);
                                                }else{
                                                    return sf.renderTable('InputNumber', params.row, h, 'bonusActual', 'prjPerformance',null)
                                                }
                                            },
                                        },
                                        {
                                            title: '调整说明',
                                            key: 'bonusAdjustDesc',
                                            width: 150,
                                            render: (h, params) => {
                                                if(sf.isPfmAdmin || sf.isViewLeader  || !params.row.isPfmEmp || (sf.editKey != 'bonusAdjustDesc' || sf.editRow != params.row._index)){
                                                    return h('div',{
                                                        class:'t-edit-warp',
                                                        on:{
                                                            click:function(){
                                                                sf.editKey =  'bonusAdjustDesc';
                                                                sf.editRow = params.row._index;
                                                            }
                                                        }
                                                    },[
                                                        h('span',params.row.bonusAdjustDesc),
                                                        h((sf.isPfmAdmin || !params.row.isPfmEmp || sf.isViewLeader)  ? null : 'i',{
                                                            class:'iconfont icon-edit',
                                                        })
                                                    ]);
                                                }else{
                                                    return sf.renderTable('Input', params.row, h, 'bonusAdjustDesc', 'prjPerformance',null)
                                                }
                                            },
                                        },
                                        {
                                            title: '实际计划偏差率',
                                            key: 'actualPlanDeviationRate',
                                            width: 120,
                                            render: (h, params) => {
                                                var bonusDue = params.row.bonusDue || 0;
                                                var bonusActual = params.row.bonusActual || 0;
                                                if(bonusDue && bonusDue.indexOf('*') > -1){
                                                    return h('span', '****');
                                                }else{
                                                    return h('span',bonusActual - bonusDue);
                                                }
                                            }
                                        },
                                        {
                                            title: '偏差率',
                                            key: 'deviation',
                                            width: 120,
                                            render: (h, params) => {
                                                var bonusDue = params.row.bonusDue || 0;
                                                var bonusActual = params.row.bonusActual || 0;
                                                if(bonusDue && bonusDue.indexOf('*') > -1){
                                                    return h('span', '****');
                                                }else{
                                                    if(bonusDue === 0){
                                                        return h('span', '0%');
                                                    }else{
                                                        return h('span', ((bonusActual - bonusDue) / bonusDue*100).toFixed(2)+'%');
                                                    }
                                                }
                                            }
                                        }
                                    ];


                                    columns.splice.apply(columns, [8, 0].concat(columns1));

                                    sf.prjPerformanceColumns = columns;
                                }else{
                                    sf.getApprStatusFlowData(item);
                                }

                                isPass = false;
                            }
                        });


                        if(isPass){
                            sf.getApprStatusFlowData(null);
                        }


                        sf.getPrjBonusList();

                        sf.apprStatusFlow = apprStatusFlow;
                    } else {
                        sf.$Message.error(res.message || '获取审批流失败');
                    }
                }).catch(function(error) {
                    sf.$Message.error('获取审批流失败');
                });
            },

            getApprStatusFlowData(item){
                var sf = this;
                if(item && item.status.cid === '6821c484fb03e0e1732c4066'){
                    sf.showAdditionalReview = true;
                    sf.isAdditionalReview = 1;
                }else{
                    sf.showAdditionalReview = false;
                }
                sf.exportIndex = 4;
                sf.getPrjBonusDuration(false);
                sf.getPrjBonusDuration(true);
                sf.getPrjBonusAnalyse();
                sf.approveReject = '审批不通过';
                sf.approvePass = '审批通过';
                sf.pfmType = 'approve';
                sf.isShowResult = true;
                var columns = sf.prjPerformanceColumns2.concat([]);

                var columns1 = [
                    {
                        title: '计划发放金额(元)',
                        key: 'bonusDue',
                        width: 130,
                        sortable: true
                    },
                    { title: '实际发放金额(元)', key: 'bonusActual', width: 130,sortable: true},
                    {
                        title: '调整说明',
                        key: 'bonusAdjustDesc',
                        width: 150
                    },
                    {
                        title: '实际计划偏差率',
                        key: 'actualPlanDeviationRate',
                        width: 120,
                        render: (h, params) => {
                            var bonusDue = params.row.bonusDue || 0;
                            var bonusActual = params.row.bonusActual || 0;
                            if(bonusDue && bonusDue.indexOf('*') > -1){
                                return h('span', '****');
                            }else{
                                return h('span', bonusDue - bonusActual);
                            }
                        }
                    },
                    {
                        title: '偏差率',
                        key: 'deviation',
                        width: 120,
                        render: (h, params) => {
                            var bonusDue = params.row.bonusDue || 0;
                            var bonusActual = params.row.bonusActual || 0;
                            if(bonusDue && bonusDue.indexOf('*') > -1){
                                return h('span', '****');
                            }else{
                                if(bonusDue === 0){
                                    return h('span', '0%');
                                }else{
                                    return h('span', ((bonusDue - bonusActual) / bonusDue*100).toFixed(2)+'%');
                                }
                            }
                        }
                    }
                ];


                columns.splice.apply(columns, [8, 0].concat(columns1));
                // 删除数组第一列
                if (Array.isArray(columns) && columns.length > 0) {
                    columns.splice(0, 1);
                }

                sf.prjPerformanceColumns = columns;
            },

            // 导出项目绩效
            exportExcel(){
                var sf = this;
                sf.getHasAuth();
            },
            //复制计划金额
            copyBonusDueToActual(){
                var sf = this;
                sf.$Modal.confirm({
                    title: '提示',
                    content: '将复制全部人员计划发放金额到实际发放中，覆盖原有实际发放金额，请确认是否操作？',
                    onOk: function ()  {
                        $.ajax({
                            url:  linkus.location.prj + '/prjBonusCtrl/copyBonusDueToActual.action',
                            method: 'POST',
                            data:{
                                prjId: sf.prjId,
                                pfmApplyId: sf.pfmApplyId,
                            },
                        }).done(function (res) {
                            if(res.success){
                                sf.getPrjBonusList();

                            }else{
                                sf.$Message.error(res.message);

                            }
                        });
                    },
                    onCancel: function ()  {

                    }
                });


            },

            // 导出项目绩效
            exportPrjBonus() {
                var sf = this;
                var param = {
                    prjId: sf.prjId,
                    pfmApplyId: sf.pfmApplyId,
                    all: true,
                    index:sf.exportIndex
                };

                if (sf.pfmType === 'approve') {
                    param.title = sf.tabaleParam.title;
                    param.dept = sf.tabaleParam.dept;
                    param.group = sf.tabaleParam.group;
                    param.emp = sf.tabaleParam.emp;
                    if(sf.tabaleParam.employeeType === true || sf.tabaleParam.employeeType === false){
                        param.employeeType = sf.tabaleParam.employeeType;
                    }
                    if(sf.tabaleParam.isPfmEmp === true || sf.tabaleParam.isPfmEmp === false){
                        param.isPfmEmp = sf.tabaleParam.isPfmEmp;
                    }
                }
                // 构建查询参数字符串
                var query = Object.keys(param)
                    .filter(function(key) { return param[key] !== undefined && param[key] !== null && param[key] !== ''; })
                    .map(function(key) {
                        if (typeof param[key] === 'object') {
                            return encodeURIComponent(key) + '=' + encodeURIComponent(JSON.stringify(param[key]));
                        }
                        return encodeURIComponent(key) + '=' + encodeURIComponent(param[key]);
                    })
                    .join('&');
                // 拼接导出url
                var url = '/linkus-prj/prjBonusCtrl/exportPrjBonus.action?' + query;
                sf.exportUrl = url;
                sf.viewMoneyModal = false;
            },

            // 获取项目绩效列表
            getPrjBonusList() {
                var sf = this;
                var param = {
                    prjId: sf.prjId,
                    pfmApplyId:sf.pfmApplyId
                };
                if(sf.isPfmAdmin || sf.isViewLeader || sf.pfmType === 'approve'){
                    param.all = true;
                }
                param.title = sf.tabaleParam.title;
                param.dept = sf.tabaleParam.dept;
                param.group = sf.tabaleParam.group;
                param.emp = sf.tabaleParam.emp;
                if(sf.tabaleParam.employeeType === true || sf.tabaleParam.employeeType === false){
                    param.employeeType = sf.tabaleParam.employeeType;
                }
                if(sf.tabaleParam.isPfmEmp === true || sf.tabaleParam.isPfmEmp === false){
                    param.isPfmEmp = sf.tabaleParam.isPfmEmp;
                }
                sf.prjPerformanceLoading = true;
                return Ajax.send({
                    url: '/linkus-prj/prjBonusCtrl/queryPrjBonus.action',
                    method: 'POST',
                    data: param
                }).then(function(res) {
                    sf.prjPerformanceLoading = false;
                    if(res.success){
                        if(!param.title && !param.dept && !param.group && !param.emp && !param.isPfmEmp && param.isPfmEmp !== false && !param.employeeType && param.employeeType !== false){
                            sf.prjPerformanceData = res.data || [];
                        }
                        sf.prjPerformanceData1 = res.data || [];
                        if(!param.title && !param.dept && !param.group && !param.emp && !param.isPfmEmp && param.isPfmEmp !== false && !param.employeeType && param.employeeType !== false){
                            sf.bonusActual = 0;
                            sf.tableOpts.checkPsn = res.data.map(item => {
                                if(sf.pfmType === 'pmApprove' || sf.pfmType === 'approve'){
                                    sf.bonusActual += Number(item.bonusActual || 0)
                                };
                                return {
                                    label: item.emp.userName+'/'+item.emp.loginName,
                                    value: item.emp.userId,
                                    userId: item.emp.userId,
                                    userName: item.emp.userName,
                                    loginName: item.emp.loginName,
                                    jobCode: item.emp.jobCode
                                }
                            });
                        }
                        if((sf.pfmApply.bonusActual - sf.bonusActual) < 0){
                            sf.$Message.error('实际应发金额大于计划，请调整');
                            return;
                        };
                        if(sf.pfmType === 'approve' && sf.isShowResult){
                            sf.getPrjBonusRoleReport(true);
                            sf.getPrjBonusRoleReport(false);
                        }

                    } else {
                        sf.$Message.error(res.message || '获取项目绩效列表失败');
                    }
                }).catch(function(error) {
                    sf.$Message.error('获取项目绩效列表失败');
                });
            },


            getOpts(){
                var sf = this;
                //查询岗位
                sf.queryOpt('5a70111329974f7cabe9115d', 'PMS_PRJ_EMP_TITLE').then(function(res) {
                    var title = res.map(item => {
                        return {
                            label: item.defName,
                            value: item.id,
                            cid:item.id,
                            codeName:item.codeName,
                            name:item.defName,
                            ratio:item.value,
                            defNo:item.defNo
                        }
                    });
                    sf.tableOpts.title = title;
                    sf.selectDataMap.title = title;
                });
            },

            // 通用下拉查询
            queryOpt: function(srcDefId, codeName) {
                return Ajax.send({
                    url: linkus.location.prjuser +'/sysDefCtrl/getSysDefBySrcAndCode.action',
                    method: 'POST',
                    data: {
                        srcDefId: srcDefId,
                        codeName: codeName
                    }
                });
            },

            // 加载初始化项目
            loadInitPrjInfo:function (prjId) {
                var sf = this;
                var queryUrl = linkus.location.prj + '/prjInfoCtrl/queryByClickTimeMax.action';
                if (prjId) {
                    queryUrl = linkus.location.prj + '/prjInfoCtrl/queryPrjInfoByPrjId.action?prjId=' + prjId;
                }
                return $.ajax({
                    url: queryUrl,
                    method: 'GET',
                }).done(function (tePrjInfoVo) {
                    sf.prjId = tePrjInfoVo.prjId;
                    sf.prjName = tePrjInfoVo.prjName;
                    sf.prjCode = tePrjInfoVo.prjCode;
                    sf.prjIdValue = tePrjInfoVo.prjIdValue;
                    sf.selectPrjSbuId = tePrjInfoVo.sbuId;
                    sf.pmUser = tePrjInfoVo.pmUser;
                    sf.isPm = sf.pmUser.userId === sf.currentUser.id;
                    sf.prjBasicInfo.level = tePrjInfoVo.level.name;
                    sf.prjBasicInfo.province =  tePrjInfoVo.bigRegion.name + "/" + tePrjInfoVo.prov.name;
                    sf.prjBasicInfo.pmUser = tePrjInfoVo.pmUser.userName+'/'+tePrjInfoVo.pmUser.loginName;
                });
            },


            // 发起绩效评分
            initPrj(){
                var sf = this;
                sf.$refs.initPrjData.validate((valid) => {
                    if (valid) {
                        if(sf.initPrjLoading){
                            return;
                        }
                        sf.initPrjLoading = true;

                        let param = {
                            startYm: sf.initPrjData.startMonth.format('yyyy-MM'),
                            endYm: sf.initPrjData.endMonth.format('yyyy-MM'),
                            site: sf.initPrjData.projectLocation.toString()
                        }
                        let url = '';
                        let message = '发起绩效评分'
                        if(sf.updatePrj){
                            url = '/linkus-prj/prjBonusCtrl/update.action';
                            param.pfmApplyId = sf.pfmApplyId;
                            message = '更新绩效评分'
                        }else{
                            url = '/linkus-prj/prjBonusCtrl/initPrjBonus.action';
                            param.prjId = sf.prjId

                        }

                        Ajax.send({
                            url:url ,
                            method: 'POST',
                            data: param,
                        }).then(function(response) {
                            if(response.success){
                                sf.$Message.success(message+'成功');
                                sf.initPrjModal = false;
                                sf.initPrjData.startMonth = null;
                                sf.initPrjData.endMonth = null;
                                sf.initPrjData.projectLocation = null;
                                sf.getApplyHist(sf.pfmApplyId);
                                sf.listPrjEmpPfmApplyByPrjId(sf.prjId);
                                sf.initPrjLoading = false;
                                sf.updatePrj = false;
                            }else{
                                sf.initPrjLoading = false;
                                sf.$Message.error(response.message || message+'失败');
                            }
                        }).catch(function(error) {
                            sf.$Message.error(message+'失败');
                            sf.initPrjLoading = false;
                        });
                    }
                });
            },

            renderTable(type,row,h,key,tableType,dataType,objId){
                var sf = this;
                if(type === 'DatePicker'){
                    if(!sf.isPmOrPrjAdmin){
                        if(!!row[key]){
                            return h('span',row[key].substring(0,10));
                        }else{
                            return null;
                        }
                    }

                    return h('DatePicker', {
                        props: {
                            format: 'yyyy-MM-dd',
                            transfer:true,
                            clearable:true,
                            size:'small',
                            type:'date',
                            value:row[key]
                        },
                        on: {
                            'on-change': function(date) {
                                row[key] = date;
                                sf.setData(row,row._index);
                                if(sf.pfmType != 'submit' || sf.isPm || sf.isPfmAdmin){
                                    sf.editTable(row);
                                }
                            },
                        }
                    });
                }else if(type === 'Input'){
                    return h('Input', {
                        props:{
                            clearable:true,
                            size:'small',
                            value: row[key]
                        },
                        style:{
                            width: 'calc(100% - 25px)',
                        },
                        on: {
                            input: function (event) {
                                row[key] = event
                            },
                            'on-blur':function (event) {
                                sf.setData(row,row._index);
                                if(sf.pfmType != 'submit' || sf.isPm || sf.isPfmAdmin){
                                    sf.editTable(row);
                                }
                            },
                            'on-clear':function (event) {
                                sf.setData(row,row._index);
                                if(sf.pfmType != 'submit' || sf.isPm || sf.isPfmAdmin){
                                    sf.editTable(row);
                                }
                            }
                        }
                    });
                }else if(type === 'Select'){
                    var attr = [];
                    if(!!sf.tableOpts[key]){
                        attr = sf.tableOpts[key].map((obj) => {
                            return h('Option', {
                                props: {
                                    label: obj.label,
                                    value: obj.value,
                                    disabled: key === 'checkPsn' && row.emp.userId === obj.value ? true : false
                                }
                            });
                        })
                    }

                    return h('Select', {
                            props: {
                                value: dataType === 'obj' && !!row[key] ? row[key][objId] : row[key],
                                transfer:true,
                                size:'small',
                                filterable:true,
                            },
                            on: {
                                'on-change': function(value) {
                                    var obj = sf.tableOpts[key].filter(item => item.value === value)[0];
                                    row[key] = obj;
                                    sf.setData(row,row._index);
                                    if(key === 'title'){
                                        var title = sf.tableOpts[key].filter(item => item.value === row.title.cid)[0];
                                        row.titleRatio = title.ratio;
                                        row.prjMgtRatio = null;
                                        sf.$nextTick(() => {
                                            if(sf.pfmType != 'submit' || sf.isPm || sf.isPfmAdmin){
                                                sf.editTable(row);
                                            }
                                            sf.setScore(row,key);
                                        });
                                    }else{
                                        if(sf.pfmType != 'submit' || sf.isPm || sf.isPfmAdmin){
                                            sf.editTable(row);
                                        }
                                    }
                                }
                            }
                        },
                        attr
                    );
                }else if(type === 'InputNumber'){
                    var value =  row[key]
                    if(key === 'workEffortScore' || key === 'workQualityScore'){
                        value = row[key].score;
                    }

                    var props = {
                        value: value,
                        size:'small',
                    }
                    if(key === 'workEffortScore' || key === 'workQualityScore' || key === 'initiativeScore' || key=== 'comSkillScore' || key === 'hardWorkScore' ){
                        props.max = 100;
                        props.min = 0;
                        props.precision = 0;
                    }

                    return h('InputNumber', {
                        props: props,
                        on: {
                            input: function (event) {
                                if(key === 'workEffortScore' || key === 'workQualityScore'){
                                    row[key].score = event
                                }else{
                                    row[key] = event
                                }
                            },
                            'on-blur':function (event) {
                                sf.setScore(row,key);
                                sf.setData(row,row._index);
                                sf.$nextTick(() => {
                                    if(sf.pfmType != 'submit' || sf.isPm || sf.isPfmAdmin){
                                        sf.editTable(row);
                                    }
                                });
                            },
                            'on-clear':function (event) {
                                sf.setScore(row,key);
                                sf.setData(row,row._index);
                                sf.$nextTick(() => {
                                    if(sf.pfmType != 'submit' || sf.isPm || sf.isPfmAdmin){
                                        sf.editTable(row);
                                    }
                                });
                            }
                        }
                    });
                }else if(type === 'lks-load-user-fuzzy'){
                    var user = {};
                    var label = '';
                    var id = '';
                    if(row[key]){
                        user = row[key];
                        label = user.userName+'/'+user.loginName;
                        id =  user.userId;
                    }

                    return h('prj-load-user-fuzzy-table', {
                        props:{
                            size:'small',
                            value:id,
                            userId:id,
                            thisJson:JSON.stringify(user),
                            queryName:label,
                            rowData:row,
                            rowKey:key,
                            clearable:true,
                        },
                        style:{
                            width: 'calc(100% - 25px)'
                        },
                        on:{
                            'loadUserSelectTable':function(data){
                                row[key] = JSON.parse(data);
                                sf.setData(row,row._index);
                                if(sf.pfmType != 'submit' || sf.isPm || sf.isPfmAdmin){
                                    sf.editTable(row);
                                }
                            }
                        }
                    })
                }else if(type === 'i-switch'){
                    return  h('i-switch',{
                        props: {
                            value:row[key] || false,
                            disabled:false,//禁用开关,
                        },
                        on:{
                            'on-change': function (value) {
                                row[key] = value;
                                sf.setData(row,row._index);
                                if(sf.pfmType != 'submit' || sf.isPm || sf.isPfmAdmin){
                                    sf.editTable(row);
                                }
                            }
                        }
                    },[
                        h('span',{slot:'open'},'是'),
                        h('span',{slot:'close'},'否'),
                    ]);
                }
            },


            setData(row,index){
                var sf = this;
                sf.prjPerformanceData1[index] = row;
            },


            editTableAll(){
                var sf = this;
                var empPfms = [];
                sf.prjPerformanceData1.forEach(function(item){
                    var params = item;
                    if(sf.isEncryption){
                        // 剔除 bonusActual 和 bonusDue 字段
                        if ('bonusActual' in params) {
                            delete params.bonusActual;
                        }
                        if ('bonusDue' in params) {
                            delete params.bonusDue;
                        }

                        if ('bonusAdjust' in params) {
                            delete params.bonusAdjust;
                        }
                    };
                    empPfms.push(params);
                });

                var msgLoading = sf.$Message.loading({
                    content: '正在保存中...',
                    duration: 0
                });

                $.ajax({
                    url: '/linkus-prj/prjBonusCtrl/bacthUpdatePrjBonus.action',
                    type: 'POST',
                    data: JSON.stringify(empPfms),
                    contentType: 'application/json',
                    success: function(res) {
                        if(res.success){
                            sf.$Message.destroy(msgLoading)
                            sf.$Message.success('保存成功');

                            sf.getPrjBonusList();
                        }else{
                            sf.$Message.error(res.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        sf.$Message.error('更新绩效评分失败');
                    }
                });
            },

            editTable(row,key) {
                var sf = this;
                var params = {...row};

                if(params.employeeType == '正式'){
                    params.employeeType = 'Employee';
                }else if(params.employeeType == '外包'){
                    params.employeeType = 'Outsource';
                }

                if(sf.isEncryption){
                    // 剔除 bonusActual 和 bonusDue 字段
                    if ('bonusActual' in params) {
                        delete params.bonusActual;
                    }
                    if ('bonusDue' in params) {
                        delete params.bonusDue;
                    }

                    if ('bonusAdjust' in params) {
                        delete params.bonusAdjust;
                    }
                }

                $.ajax({
                    url: '/linkus-prj/prjBonusCtrl/updatePrjBonus.action',
                    type: 'POST',
                    data: JSON.stringify(params),
                    contentType: 'application/json',
                    success: function(res) {
                        sf.editKey = '';
                        sf.editRow = '';
                        if(res.success){
                            sf.getPrjBonusList();
                        }else{
                            sf.$Message.error(res.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        sf.$Message.error('更新绩效评分失败');
                    }
                });
            },



            addUser(){
                var sf = this;
                sf.addUserModal = true;
            },

            findUserByBu(){
                var sf = this;
                Ajax.send({
                    url: '/linkus-sys-user/sysUserCtrl/findUserByBu.action',
                    method: 'GET',
                    data:{
                        buId:'185',
                    }
                }).then(function(res) {
                    var userList = res || [];
                    sf.userListBase = userList.filter(item => item.employeeType !== 'Trainee');
                });
            },
            // 人员下拉框
            userRemoteMethod(query){
                var sf = this;
                sf.userList = [];
                var userList = [];
                sf.userLoading = true;

                if(!query){
                    return;
                }

                if (sf.timer) {
                    clearTimeout(sf.timer);
                    sf.timer = null;
                }
                sf.timer = setTimeout(() => {
                    sf.userListBase.forEach(item => {
                        if(item.userName.includes(query) || item.loginName.includes(query)){
                            userList.push(item);
                        }
                    });
                    sf.userList = userList;
                    sf.userLoading = false;
                }, 1000);
            },

            deleteUser(){
                var sf = this;
                // 获取iview表格选中数据
                if (!sf.selectedRows || sf.selectedRows.length === 0) {
                    sf.$Message.warning('请先选择要删除的人员');
                    return;
                }
                let ids = [];
                var msg = '';
                sf.selectedRows.forEach(row => {
                    if(row.mdStd === 0){
                        ids.push(row.id);
                    }else{
                        msg += row.emp.userName + '/' + row.emp.loginName + '，';
                    }
                });

                if(msg){
                    msg = msg+ '在项目下存在有效工时，不允许删除！';
                    sf.$Message.warning(msg);
                    return;
                }



                sf.$Modal.confirm({
                    title: '提示',
                    content: '确定要删除选中的人员吗？',
                    onOk: function () {
                        Ajax.send({
                            url: '/linkus-prj/prjBonusCtrl/deletePrjBonus.action',
                            method: 'POST',
                            data: {
                                prjEmpPfmIds: ids.join('，')
                            }
                        }).then(function(res) {
                            if (res.success) {
                                sf.$Message.success('删除成功');
                                sf.listPrjEmpPfmApplyByPrjId(sf.prjId);
                            } else {
                                sf.$Message.error(res.message || '删除失败');
                            }
                        }).catch(function(error) {
                            sf.$Message.error('删除失败');
                        });
                    }
                });
            },

            insertPrjBonus(){
                var sf = this;
                sf.$refs.addUserData.validate((valid) => {
                    if (valid) {
                        var userData = sf.prjPerformanceData.map(item => {
                            return item.emp.userId;
                        });

                        let dataListBase = sf.addUserData.userList.map(item => {
                            return {
                                prj:{cid:sf.prjId},
                                emp:{userId:item},
                                title:{cid:'682442cffb03e0e17383b7ea',codeName:'regularEmp',name:'普通成员'},
                                pfmApplyId:sf.pfmApplyId,
                                isPfmEmp:true,
                                mdAdjust:0,
                            }
                        });

                        dataList = dataListBase.filter(item => {
                            return !userData.includes(item.emp.userId);
                        });

                        if(dataList.length === 0){
                            var list = sf.prjPerformanceData.filter(item => {
                                return sf.addUserData.userList.includes(item.emp.userId);
                            });
                            var msg = list.map(item => {
                                return item.emp.userName + '/' + item.emp.loginName;
                            }).join('，');
                            sf.$Message.warning(msg+'已存在，请修改后操作！');
                            return;
                        };

                        if(sf.isAddUserLoading){
                            return;
                        }
                        sf.isAddUserLoading = true;

                        Ajax.send({
                            url: '/linkus-prj/prjBonusCtrl/insertPrjBonus.action',
                            method: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify(dataList)
                        }).then(function(res) {
                            if(res.success){
                                sf.$Message.success('新增绩效评分成功');
                                sf.addUserModal = false;
                                sf.isAddUserLoading = false;
                                sf.getPrjBonusList();
                                sf.addUserData.userList = [];
                                sf.$nextTick(() => {
                                    sf.$refs.addUserData.resetFields();
                                });

                            }else{
                                sf.$Message.error(res.message || '新增绩效评分失败');
                            }
                        }).catch(function(error) {
                            sf.$Message.error('新增绩效评分失败');
                        });
                    }
                });
            },


            // 启动绩效评分
            startPrjPfm(type){
                var sf = this;
                var flag = false;

                if(sf.pfmType === 'pmApprove' && !sf.isEncryption){
                    if((sf.pfmApply.bonusActual - sf.bonusActual) != 0){
                        sf.$Message.error('剩余待分配'+(sf.pfmApply.bonusActual - sf.bonusActual)+'元，请全部分配完成后提交');
                        return;
                    }
                }
                var msg = '';
                for(var i = 0; i < sf.prjPerformanceData.length; i++){
                    var item = sf.prjPerformanceData[i];
                    var userName = item.emp.userName;
                    var loginName = item.emp.loginName;
                    if(!item.isPfmEmp){
                        continue;
                    }
                    // 校验岗位、归属组、打分人，增补工时天数、增补说明（若增补天数≠0时）不允许为空
                    if (!item.title) {
                        msg += '岗位不能为空，';
                        flag = true;
                    }
                    if (!item.group) {
                        msg += '归属组不能为空，';
                        flag = true;
                    }
                    if (!item.checkPsn) {
                        msg += '打分人不能为空，';
                        flag = true;
                    }
                    // 增补工时天数、增补说明（若增补天数≠0时）不允许为空
                    if (item.mdAdjust !== 0 && !item.mdAdjustDesc) {
                        msg += '增补说明不能为空，';
                        flag = true;
                    }

                    // 参与激励且实际金额不能为空
                    if (sf.pfmType === 'pmApprove' && item.isPfmEmp && !sf.isEncryption && item.bonusActual === null) {
                        msg += '实际发放金额不能为空，';
                        flag = true;
                    }


                    if(flag){
                        msg =  userName + '/' + loginName  +  msg;
                        if (msg.endsWith('，')) {
                            msg = msg.slice(0, -1);
                        }
                        msg += '，请修改后再次提交！';
                        break;
                    };



                    if(sf.pfmType === 'pmApprove' && !sf.isEncryption && item.isPfmEmp){
                        var bonusDue = item.bonusDue || 0;
                        var bonusActual = item.bonusActual || 0;
                        var deviation = (((bonusActual - bonusDue) / bonusDue)*100).toFixed(2);



                        if(((bonusActual - bonusDue) >= 1000  || ((deviation > 10 || deviation < -10) && bonusActual > 200 && bonusDue > 200 ) ) && !item.bonusAdjustDesc){
                            msg += '超额调整，请填写调整说明';
                            flag = true;
                        }
                    }


                    if(flag){
                        msg =  userName + '/' + loginName  +  msg;
                        if (msg.endsWith('，')) {
                            msg = msg.slice(0, -1);
                        }
                        break;
                    }
                }

                if(flag){
                    sf.$Message.error({
                        content: msg,
                        duration: 5,
                    });
                    return;
                }else{
                    Ajax.send({
                        url: '/linkus-prj/prjBonusCtrl/submitPrjBonus.action',
                        method: 'POST',
                        data:{
                            prjId:sf.prjId,
                            pfmApplyId:sf.pfmApplyId,
                            passBranch:sf.showAdditionalReview ? sf.isAdditionalReview : 0,
                            desc:type === 'pass' ? sf.auditDesc : '',
                        }
                    }).then(function(res) {
                        if(res.success){
                            sf.getApprStatusFlow();
                            sf.getApplyHist(sf.pfmApplyId);
                            if(type === 'pass'){
                                location.reload();
                            }
                        }else{
                            sf.$Message.error({
                                content: res.message || '启动绩效评分失败',
                                duration: 3
                            });
                        }
                    }).catch(function(error) {

                    });
                }
            },

            //计算基础得分、个人绩效分
            setScore(row,key){
                var sf = this;
                if(key === 'workEffortScore' || key === 'workQualityScore' || key === 'initiativeScore' || key=== 'comSkillScore' || key === 'hardWorkScore' ){
                    var workEffortScore = row.workEffortScore && row.workEffortScore.score ? row.workEffortScore.score : 0;
                    var workQualityScore = row.workQualityScore &&  row.workQualityScore.score ? row.workQualityScore.score : 0;
                    var initiativeScore = row.initiativeScore || 0;
                    var comSkillScore = row.comSkillScore || 0;
                    var hardWorkScore = row.hardWorkScore || 0;
                    var baseScore = ((workEffortScore*0.5 + workQualityScore*0.5)*0.8 + (initiativeScore*0.5 + comSkillScore*0.2 + hardWorkScore*0.3)*0.2).toFixed(1);
                    row.baseScore = baseScore;

                    var titleRatio = row.titleRatio || 0;
                    var mdRatio = row.mdRatio  == null || row.mdRatio === undefined ? 1 : row.mdRatio;
                    var tripRatio = row.tripRatio === null || row.tripRatio === undefined ? 1 : row.tripRatio;
                    var empPfmRatio = baseScore*titleRatio*mdRatio*tripRatio;
                    row.empPfmRatio = empPfmRatio.toFixed(2);
                }
            },



            updatePrjEmpPfmApply() {
                var sf = this;
                var param = {
                    pfmApplyId:sf.pfmApplyId,
                    prePrjMm:sf.pfmApply.prePrjMm,
                    bonusExtra:sf.pfmApply.bonusExtra,
                };
                Ajax.send({
                    url: '/linkus-prj/prjBonusCtrl/updatePrjEmpPfmApply.action',
                    method: 'POST',
                    data: param
                }).then(function(res) {
                    if (res.success) {
                        sf.listPrjEmpPfmApplyByPrjId(sf.prjId,'reset');
                    } else {
                        sf.$Message.error(res.message || '更新失败');
                    }
                }).catch(function(error) {
                    sf.$Message.error('更新失败');
                });
            },


            getHisHeight:function(){
                var sf =this;
                sf.$nextTick(function () {
                    sf.hisHeight = document.documentElement.clientHeight - 112 + 'px';
                    let height = document.documentElement.clientHeight - 112;
                    let i= 0;
                    var timer = setInterval(function(){
                        height+=1;
                        i++
                        sf.hisHeight = height + 'px';
                        if(i>20){
                            clearInterval(timer);
                        }
                    },50);
                })
            },




            getAuditHists(list){
                var sf = this;
                var hisList = [];
                list.forEach(item => {
                    if(item.allPassIsRequired){
                        let toCheckEmps = item.toCheckEmps || [];
                        var baseItem = JSON.parse(JSON.stringify(item));
                        if(item.status.codeName === 'pending'){
                            let pendingStatus = baseItem.status;
                            passNotNullList = toCheckEmps.filter(item => item.isPass != null);
                            if(passNotNullList.length > 0){
                                passNotNullList.forEach(emp => {
                                    baseItem = JSON.parse(JSON.stringify(item));
                                    if(emp.isPass){
                                        baseItem.status = sf.getStatus(true);
                                    }else{
                                        baseItem.status = sf.getStatus(false);
                                    }
                                    baseItem.toCheckEmps = [emp];
                                    hisList = hisList.concat([baseItem]);
                                });
                            }

                            baseItem = JSON.parse(JSON.stringify(item));
                            passNullList = toCheckEmps.filter(item => item.isPass == null);
                            baseItem.status = pendingStatus;
                            baseItem.toCheckEmps = passNullList;
                            hisList = hisList.concat([baseItem]);
                        }else{
                            passNotNullList = toCheckEmps.filter(item => item.isPass != null);
                            if(passNotNullList.length > 0){
                                passNotNullList.forEach(emp => {
                                    baseItem = JSON.parse(JSON.stringify(item));
                                    if(emp.isPass){
                                        baseItem.status = sf.getStatus(true);
                                    }else{
                                        baseItem.status = sf.getStatus(false);
                                    }
                                    baseItem.toCheckEmps = [emp];
                                    hisList = hisList.concat([baseItem]);
                                });
                            }
                        }
                    }else{
                        hisList = hisList.concat([item]);
                    }
                });
                return hisList;
            },

            getStatus(isPass){
                var sf = this;
                if(isPass){
                    return {
                        cid:'5dff6a4bc56a424c33470504',
                        codeName:'isApproved',
                        name:'审批通过',
                    };
                }else{
                    return {
                        cid:'5dff6a62c56a424c33470505',
                        codeName:'isFailed',
                        name:'审批不通过',
                    };
                }
            },


            // 调用sysUserApplyCtrl/getApplyHist.action
            getApplyHist(rcdId) {
                var sf = this;
                Ajax.send({
                    url: '/linkus-sys-user/sysUserApplyCtrl/getApplyHist.action',
                    method: 'POST',
                    data: {
                        rcdId: rcdId
                    }
                }).then(function(res) {
                    if (res.success) {
                        // 处理返回的历史数据
                        var auditHists = sf.getAuditHists(res.data || []);
                        var hisList = [];
                        auditHists.forEach((item,i) => {
                            item.nodeCodeName = item.status.codeName;
                            var toCheckEmps = item.toCheckEmps || [];
                            if(item.status.codeName === 'pending'){
                                var resUserLogin = [];
                                toCheckEmps.forEach(function (res,i){
                                    resUserLogin.push(res.emp.userName+'/'+res.emp.loginName);
                                });
                                item.resUserLogin = resUserLogin.join('，');
                                sf.currentStep = i;
                                sf.currentStepCheckEmp = toCheckEmps.map(emp => {return emp.emp});
                            }else if(item.status.codeName === 'isApproved'){
                                var passEmps = {};
                                passEmps =  toCheckEmps.find(item => item.isPass === true);
                                item.auditUser = passEmps.emp;
                                item.auditTime = passEmps.checkTime;
                                item.auditDesc = passEmps.desc;
                            }else if(item.status.codeName === 'isFailed'){
                                var passEmps = {};
                                passEmps =  toCheckEmps.find(item => item.isPass === false);
                                item.auditUser = passEmps.emp;
                                item.auditTime = passEmps.checkTime;
                                item.auditDesc = passEmps.desc;
                            }
                            hisList.push(item);

                        });
                        if(sf.currentStep === -1){
                            sf.currentStep = hisList.length;
                        }
                        sf.auditHists = hisList;
                    } else {
                        sf.$Message.error(res.message || '获取申请历史失败');
                    }
                }).catch(function(error) {

                });
            },


            getIcon:function(nodeCodeName){
                var sf = this;
                if(nodeCodeName === 'isApproved'){
                    return 'ios-checkmark-circle';
                }else if(nodeCodeName === 'isFailed'){
                    return 'ios-close-circle';
                }else{
                    return 'ios-disc';
                }
            },

            getCheckEmp(){
                var sf = this;
                var checkEmp = sf.currentStepCheckEmp.find(item => item.userId === sf.currentUser.id);
                return checkEmp;
            },

            approvePfm(){
                var sf = this;
                if(sf.approveType === 'reject'){
                    if(!sf.auditDesc){
                        sf.$Message.error('请输入审批意见');
                        return;
                    };
                    Ajax.send({
                        url: '/linkus-prj/prjBonusCtrl/rejectPrjBonus.action',
                        method: 'POST',
                        data: {
                            prjId:sf.prjId,
                            pfmApplyId:sf.pfmApplyId,
                            passBranch:0,
                            desc: sf.auditDesc
                        }
                    }).then(function(res) {
                        if(res.success){
                            sf.$Message.success('驳回成功');
                            sf.approveModal = false;
                            location.reload();
                        }else{
                            sf.$Message.error(res.message || '驳回失败');
                        }
                    }).catch(function(error) {
                        sf.$Message.error('驳回失败');
                    });


                }else{
                    sf.startPrjPfm('pass');
                }
                sf.approveModal = false;
            },


            // 调用 prjBonusDuration.action
            getPrjBonusDuration(actual) {
                var sf = this;
                var param = {
                    prjId: sf.prjId,
                    pfmApplyId: sf.pfmApplyId,
                    actual:actual,
                }
                Ajax.send({
                    url: '/linkus-prj/prjBonusCtrl/prjBonusDuration.action',
                    method: 'POST',
                    data: param
                }).then(function(res) {
                    if (res.success) {
                        var data = res.data || [];
                        var tabaleData = [];
                        var columns = [];
                        data.forEach(item => {
                            if(item.index === 0){
                                columns = [
                                    {
                                        title: item.prjDuration,
                                        key: 'onSite',
                                        width: 80,
                                        render: (h, params) => {
                                            if(params.row.onSite === '合计'){
                                                return h('div',{
                                                    style: {
                                                        fontWeight: 'bold',
                                                    }
                                                }, params.row.onSite);
                                            }else{
                                                var _index = params.index;
                                                var end = sf.planBonusData[_index].onSite;
                                                var star = sf.planBonusData[_index+1].onSite != '合计' ?  sf.planBonusData[_index+1].onSite : 0;
                                                var title = star+'<时长<='+end;
                                                return h('div', [
                                                    h('Tooltip', {
                                                        props: {
                                                            placement: 'top',
                                                            transfer: true,
                                                            theme: 'dark',
                                                            'max-width': 450
                                                        }
                                                    }, [
                                                        h('div',{class:'ellipsis'},params.row.onSite),
                                                        h('div', {
                                                                slot: 'content',
                                                                style: {
                                                                    whiteSpace: 'normal',
                                                                    fontSize: '12px',
                                                                }
                                                            }, title
                                                        )
                                                    ])])
                                            }
                                        }
                                    }
                                ]
                                var prevNum = 0;
                                item.bonus.forEach((bonus,i) => {
                                    var title = '';
                                    if(bonus != '时长/激励' && bonus.indexOf('*') === -1){
                                        var star = prevNum;
                                        prevNum = bonus;
                                        var end = bonus;
                                        title =  star+'<激励<='+end;

                                    }
                                    columns.push({
                                        title:title,
                                        key: 'col'+i ,
                                        renderHeader:(h,{column ,index})=>{
                                            return h('div', [
                                                h('Tooltip', {
                                                    props: {
                                                        placement: 'top',
                                                        transfer: true,
                                                        theme: 'dark',
                                                        'max-width': 450
                                                    }
                                                }, [
                                                    h('div',{class:'ellipsis'},bonus),
                                                    h('div', {
                                                            slot: 'content',
                                                            style: {
                                                                whiteSpace: 'normal',
                                                                fontSize: '12px',
                                                            }
                                                        }, title
                                                    )
                                                ])])
                                        },
                                        render: (h, params) => {
                                            if(params.row.onSite === '合计'){
                                                return h('div',{
                                                    style: {
                                                        fontWeight: 'bold',
                                                    }
                                                }, params.row['col'+i]);
                                            }else{
                                                return h('div', params.row['col'+i]);
                                            }
                                        }
                                    })
                                })
                            }else{
                                let row = {};
                                row.onSite = item.prjDuration;
                                item.bonus.forEach((bonus,i) => {
                                    row['col'+i] = bonus;
                                });
                                tabaleData.push(row);
                            }

                        });
                        let row = {};
                        tabaleData.forEach(item => {
                            Object.keys(item).forEach(function(key) {
                                if (row.hasOwnProperty(key)) {
                                    // 如果已经有该key，则累加
                                    // 只对数值类型进行累加
                                    var val1 = Number(row[key]);
                                    var val2 = Number(item[key]);
                                    if (!isNaN(val1) && !isNaN(val2)) {
                                        row[key] = val1 + val2;
                                    } else {
                                        row[key] = item[key];
                                    }
                                } else {
                                    row[key] = item[key];
                                }
                            });
                            row.onSite = '合计';

                        });

                        tabaleData.push(row);

                        if(actual){
                            sf.actualBonusColumns = columns;
                            sf.actualBonusData = tabaleData;
                        }else{
                            sf.planBonusColumns = columns;
                            sf.planBonusData = tabaleData;
                        }
                    }else{
                        sf.$Message.error(res.message || (actual ? '获取实际奖金数据失败' :  '获取计划奖金数据失败'));
                    }
                }).catch(function(error) {
                    sf.$Message.error(actual ? '获取实际奖金数据失败' : '获取计划奖金数据失败');
                });
            },

            getPrjBonusAnalyse() {
                var sf = this;
                Ajax.send({
                    url: '/linkus-prj/prjBonusCtrl/prjBonusAnalyse.action',
                    method: 'POST',
                    data: {
                        prjId: sf.prjId,
                        pfmApplyId: sf.pfmApplyId
                    }
                }).then(function(res) {
                    if (res.success) {
                        sf.bonusAnalysisColumns = [
                            {
                                title: '激励/人员',
                                key: 'interval',
                                width:70,
                                render: (h, params) => {
                                    if(params.row.interval.indexOf('*') > -1){
                                        return h('div', params.row.interval);
                                    }else{
                                        var _index = params.index;
                                        var end = sf.bonusAnalysisData[_index].interval;
                                        var star = sf.bonusAnalysisData[_index+1] ?  sf.bonusAnalysisData[_index+1].interval : 0;
                                        var title = star+'<激励<='+end;
                                        return h('div', [
                                            h('Tooltip', {
                                                props: {
                                                    placement: 'top',
                                                    transfer: true,
                                                    theme: 'dark',
                                                    'max-width': 450
                                                }
                                            }, [
                                                params.row.interval,
                                                h('div', {
                                                        slot: 'content',
                                                        style: {
                                                            whiteSpace: 'normal',
                                                            fontSize: '12px',
                                                        }
                                                    }, title
                                                )
                                            ])])
                                    }

                                }
                            },
                            {
                                title: '计划人数',
                                key: 'dueNum',
                            },
                            {
                                title: '计划占比',
                                key: 'dueRatio',
                                render: (h, params) => {
                                    return h('div', (params.row.dueRatio*100).toFixed(0) + '%');
                                }
                            },
                            {
                                title: '实际人数',
                                key: 'actualNum',
                            },
                            {
                                title: '实际占比',
                                key: 'actualRatio',
                                render: (h, params) => {
                                    return h('div', (params.row.actualRatio*100).toFixed(0) + '%');
                                }
                            }
                        ];

                        sf.bonusAnalysisData = res.data;
                    }else{
                        sf.$Message.error(res.message || '获取奖金分析数据失败');
                    }
                }).catch(function(error) {
                    // 错误处理
                });
            },


            getPrjBonusRoleReport(group) {
                var sf = this;
                let titles = sf.prjPerformanceData.map(item => item.title);
                // 剔除titles中cid相同的数据
                let uniqueTitles = [];
                let seenCids = new Set();
                titles.forEach(item => {
                    if (item && item.cid && !seenCids.has(item.cid)) {
                        seenCids.add(item.cid);
                        // 取tableOpts.title中id相同数值的defNo并赋值给item
                        let match = sf.tableOpts.title.find(function(t) {
                            return t.cid === item.cid;
                        });
                        if (match && match.defNo !== undefined) {
                            item.defNo = match.defNo;
                        }
                        uniqueTitles.push(item);
                    }
                });
                titles = uniqueTitles;

                titles.sort(function(a, b) {
                    // 如果defNo为数字字符串，转为数字比较，否则按字符串比较
                    var defNoA = isNaN(a.defNo) ? a.defNo : Number(a.defNo);
                    var defNoB = isNaN(b.defNo) ? b.defNo : Number(b.defNo);
                    if (defNoA < defNoB) return -1;
                    if (defNoA > defNoB) return 1;
                    return 0;
                });

                titles.push({
                    name: '合计',
                    codeName: 'total',
                });

                let columns = [
                    {
                        title: group ? '归属组' : '归属部门',
                        key: 'tag',
                        render: (h, params) => {
                            if(params.row.tag === '合计'){
                                return h('div',{
                                    style: {
                                        fontWeight: 'bold',
                                    }
                                }, params.row.tag);
                            }else{
                                return h('div', params.row.tag);
                            }
                        }
                    },
                ];

                columns = columns.concat(titles.map(item => ({
                    title: item.name,
                    key: item.codeName,
                    render: (h, params) => {
                        if(params.row.tag === '合计'){
                            return h('div',{
                                style: {
                                    fontWeight: 'bold',
                                }
                            }, params.row[item.codeName]);
                        }else{
                            if(item.codeName === 'total'){
                                return h('div',{
                                    style: {
                                        fontWeight: 'bold',
                                    }
                                }, params.row[item.codeName]);
                            }else{
                                return h('div', params.row[item.codeName]);

                            }
                        }
                    }
                })));

                if(group){
                    sf.distributionColumns1 = columns;
                }else{
                    sf.distributionColumns2 = columns;
                }

                Ajax.send({
                    url: '/linkus-prj/prjBonusCtrl/prjBonusRoleReport.action',
                    method: 'POST',
                    data: {
                        prjId: sf.prjId,
                        pfmApplyId: sf.pfmApplyId,
                        group:group
                    }
                }).then(function(res) {
                    if (res.success) {
                        let data = res.data || [];
                        // 将data中除tag,total的字段求和，放到row中
                        if (data.length > 0) {
                            let sumRow = { tag: '合计' };
                            // 获取所有key，排除'tag'和'total'
                            let keys = Object.keys(data[0]).filter(k => k !== 'tag');
                            keys.forEach(function(key) {
                                let sum = 0;
                                data.forEach(function(item) {
                                    let val = Number(item[key]);
                                    if (!isNaN(val)) {
                                        sum += val;
                                    }
                                });
                                sumRow[key] = sum;
                            });
                            // 追加合计行
                            data.push(sumRow);
                        }
                        if(group){
                            sf.distributionData1 = data;
                        }else{
                            sf.distributionData2 = data;
                        }

                    } else {
                        sf.$Message.error(res.message || '获取奖金角色分布数据失败');
                    }
                }).catch(function(error) {
                    // 错误处理
                });
            },

            // 调用 linkus-passport/radius/authenticate 接口
            authenticateUser() {
                var sf = this;
                let params = {  password: sf.vpnPwd }
                Ajax.send({
                    url: '/linkus-passport/radius/authenticate',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(params)
                }).then(function(res) {
                    if(res.success){
                        if(sf.isExport){
                            sf.exportPrjBonus();
                        }else{
                            location.reload();
                        }
                    }else{
                        sf.$Message.error(res.message || '认证失败');
                    }
                }).catch(function(error) {
                    // 错误处理
                    return { success: false, message: error && error.message ? error.message : '认证失败' };
                });
            },


            setFocus(){
                setTimeout(function() {
                    // $('.dataClass').find('.ivu-table-cell .ivu-input-number-input').focus();
                    // $('.dataClass').find('.ivu-table-cell .ivu-input').focus();
                },100);

            },

            handleSpan(obj){
                var rowIndex = obj.rowIndex;
                var columnIndex = obj.columnIndex;
                if (rowIndex === 0 && columnIndex === 0) {
                    return [4, 1];
                }else  if(columnIndex === 0 && (rowIndex === 1 || rowIndex === 2 || rowIndex === 3)){
                    return [0, 0];
                }

                if (rowIndex === 4 && columnIndex === 0) {
                    return [5, 1];
                }else  if(columnIndex === 0 && (rowIndex === 5 || rowIndex === 6 || rowIndex === 7 || rowIndex === 8)){
                    return [0, 0];
                }

                if (rowIndex === 9 && columnIndex === 0) {
                    return [8, 1];
                }else  if(columnIndex === 0 && (rowIndex === 10 || rowIndex === 11 || rowIndex === 12 || rowIndex === 13 || rowIndex === 14 || rowIndex === 15 || rowIndex === 16)){
                    return [0, 0];
                }

                if (rowIndex === 17 && columnIndex === 0) {
                    return [4, 1];
                }else  if(columnIndex === 0 && (rowIndex === 18 || rowIndex === 19 || rowIndex === 20)){
                    return [0, 0];
                }

                if (rowIndex === 21 && columnIndex === 0) {
                    return [4, 1];
                }else  if(columnIndex === 0 && (rowIndex === 22 || rowIndex === 23 || rowIndex === 24)){
                    return [0, 0];
                }

                if (rowIndex === 25 && columnIndex === 0) {
                    return [7, 1];
                }else  if(columnIndex === 0 && (rowIndex === 26 || rowIndex === 27 || rowIndex === 28 || rowIndex === 29 || rowIndex === 30 || rowIndex === 31)){
                    return [0, 0];
                }
            },
        }
    })
</script>
</body>
</html>
